test:
  service: Disk
  root: <%= Rails.root.join("tmp/storage") %>

local:
  service: Disk
  root: <%= Rails.root.join("storage") %>

# Use bin/rails credentials:edit to set the AWS secrets (as aws:access_key_id|secret_access_key)
# amazon:
#   service: S3
#   access_key_id: <%= Rails.application.credentials.dig(:aws, :access_key_id) %>
#   secret_access_key: <%= Rails.application.credentials.dig(:aws, :secret_access_key) %>
#   region: us-east-1
#   bucket: your_own_bucket-<%= Rails.env %>

# Remember not to checkin your GCS keyfile to a repository
# google:
#   service: GCS
#   project: your_project
#   credentials: <%= Rails.root.join("path/to/gcs.keyfile") %>
#   bucket: your_own_bucket-<%= Rails.env %>

# Use bin/rails credentials:edit to set the Azure Storage secret (as azure_storage:storage_access_key)
# microsoft:
#   service: AzureStorage
#   storage_account_name: your_account_name
#   storage_access_key: <%= Rails.application.credentials.dig(:azure_storage, :storage_access_key) %>
#   container: your_container_name-<%= Rails.env %>

# mirror:
#   service: Mirror
#   primary: local
#   mirrors: [ amazon, google, microsoft ]

outscale_uploads:
  service: S3
  access_key_id: <%= ENV['OUTSCALE_ACCESS_KEY_ID'] %>
  secret_access_key: <%= ENV['OUTSCALE_SECRET_ACCESS_KEY'] %>
  region: cloudgouv-eu-west-1
  bucket: <%= ENV.fetch('OUTSCALE_UPLOADS_BUCKET', 'mashe-fold-uploads') %>
  endpoint: https://oos.cloudgouv-eu-west-1.outscale.com
  force_path_style: true

outscale_documents:
  service: S3
  access_key_id: <%= ENV['OUTSCALE_ACCESS_KEY_ID'] %>
  secret_access_key: <%= ENV['OUTSCALE_SECRET_ACCESS_KEY'] %>
  region: cloudgouv-eu-west-1
  bucket: <%= ENV.fetch('OUTSCALE_DOCUMENTS_BUCKET', 'mashe-fold-documents') %>
  endpoint: https://oos.cloudgouv-eu-west-1.outscale.com
  force_path_style: true

outscale_storage:
  service: S3
  access_key_id: <%= ENV['OUTSCALE_ACCESS_KEY_ID'] %>
  secret_access_key: <%= ENV['OUTSCALE_SECRET_ACCESS_KEY'] %>
  region: cloudgouv-eu-west-1
  bucket: <%= ENV.fetch('OUTSCALE_STORAGE_BUCKET', 'mashe-storage') %>
  endpoint: https://oos.cloudgouv-eu-west-1.outscale.com
  force_path_style: true

outscale_storage_development:
  service: S3
  access_key_id: <%= ENV.fetch('OUTSCALE_ACCESS_KEY_ID_DEVELOPMENT', ENV['OUTSCALE_ACCESS_KEY_ID']) %>
  secret_access_key: <%= ENV.fetch('OUTSCALE_SECRET_ACCESS_KEY_DEVELOPMENT', ENV['OUTSCALE_SECRET_ACCESS_KEY']) %>
  region: cloudgouv-eu-west-1
  bucket: <%= ENV.fetch('OUTSCALE_STORAGE_DEVELOPMENT_BUCKET', 'mashe-storage-development') %>
  endpoint: https://oos.cloudgouv-eu-west-1.outscale.com
  force_path_style: true

outscale_storage_sandbox:
  service: S3
  access_key_id: <%= ENV.fetch('OUTSCALE_ACCESS_KEY_ID_SANDBOX', ENV['OUTSCALE_ACCESS_KEY_ID']) %>
  secret_access_key: <%= ENV.fetch('OUTSCALE_SECRET_ACCESS_KEY_SANDBOX', ENV['OUTSCALE_SECRET_ACCESS_KEY']) %>
  region: cloudgouv-eu-west-1
  bucket: <%= ENV.fetch('OUTSCALE_STORAGE_SANDBOX_BUCKET', 'mashe-storage-sandbox') %>
  endpoint: https://oos.cloudgouv-eu-west-1.outscale.com
  force_path_style: true

outscale_storage_demo:
  service: S3
  access_key_id: <%= ENV.fetch('OUTSCALE_ACCESS_KEY_ID_DEMO', ENV['OUTSCALE_ACCESS_KEY_ID']) %>
  secret_access_key: <%= ENV.fetch('OUTSCALE_SECRET_ACCESS_KEY_DEMO', ENV['OUTSCALE_SECRET_ACCESS_KEY']) %>
  region: cloudgouv-eu-west-1
  bucket: <%= ENV.fetch('OUTSCALE_STORAGE_DEMO_BUCKET', ENV.fetch('OUTSCALE_STORAGE_SANDBOX_BUCKET', 'mashe-storage-sandbox')) %>
  endpoint: https://oos.cloudgouv-eu-west-1.outscale.com
  force_path_style: true