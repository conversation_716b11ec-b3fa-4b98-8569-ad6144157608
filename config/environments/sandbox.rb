require "active_support/core_ext/integer/time"

Rails.application.configure do
  # Hériter des configurations de production
  config.load_defaults Rails::VERSION::STRING.to_f

  # Activer le chargement anticipé des classes
  config.eager_load = true

  # Paramètres spécifiques au sandbox qui diffèrent de la production
  config.action_mailer.default_url_options = { host: "nexus.mashe.dev", protocol: "https" }

  # Désactiver le cache en sandbox
  config.action_controller.perform_caching = false

  # Logger plus détaillé
  config.log_level = :debug

  # # Logger formaté pour logstash
  # RAILS 8
  # config.log_tags = [:request_id, ->(_request) { Time.now.iso8601(3) }]
  # config.logger   = ActiveSupport::TaggedLogging.logger($stdout)
  # RAILS 7
  config.log_tags = [:request_id, ->(_request) { Time.now.iso8601(3) }]
  config.logger = ActiveSupport::TaggedLogging.new(ActiveSupport::Logger.new($stdout))

  # Activer les assets en mode développement
  config.assets.debug = false
  config.assets.compile = false

  # Permettre les requêtes de n'importe quelle origine en sandbox
  config.hosts.clear

  # Configuration spécifique pour le stockage des fichiers
  config.active_storage.service = :outscale_storage

  # Désactiver la protection CSRF en sandbox si nécessaire
  # config.action_controller.allow_forgery_protection = false

  # Configuration des emails en sandbox
  config.action_mailer.delivery_method = :fallback_email
  config.action_mailer.raise_delivery_errors = false
  config.action_mailer.asset_host = "https://nexus.mashe.dev/"
end
