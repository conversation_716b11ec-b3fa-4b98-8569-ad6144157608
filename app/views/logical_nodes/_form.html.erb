<%# app/views/logical_nodes/_form.html.erb %>



<%= form_with(model: logical_node, url: url, local: true, data: { controller: "logical-node-form", turbo: false }) do |form| %>
  <% if logical_node.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(logical_node.errors.count, "error") %> prohibited this logical_node from being saved:</h2>
      <ul>
        <% logical_node.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="form-group">
    <%= form.label :name, "Nom de la combinaison" %>
    <%= form.text_field :name, class: "form-control" %>
  </div>

  <div class="form-group">
    <%= form.label :fallback_message, "Message si non respecté (optionnel)" %>
    <%= form.text_area :fallback_message, class: "form-control" %>
  </div>

  <div class="form-group">
    <%= form.label :operator, "Opérateur logique" %>
    <%= form.select :operator,
                    options_for_select(
                      [['ET', 'and_op'], ['OU', 'or_op'], ['NON', 'not_op']],
                      selected: logical_node.operator
                    ),
                    {},
                    { class: "form-control", data: { action: "logical-node-form#handleOperatorChange" } } %>
  </div>

  <%# ----- Préparation des valeurs pour les selects polymorphiques ----- %>
  <%
    # Pour l'élément gauche
    left_type = logical_node.left_element_type
    left_id = logical_node.left_element_id
    selected_left_value = nil
    if left_type.present? && left_id.present?
      selected_left_value = "#{String(left_type).strip}_#{String(left_id).strip}"
    end

    # Pour l'élément droit
    right_type = logical_node.right_element_type
    right_id = logical_node.right_element_id
    selected_right_value = nil
    if right_type.present? && right_id.present?
      selected_right_value = "#{String(right_type).strip}_#{String(right_id).strip}"
    end

    # Options communes pour les deux selects
    grouped_element_options = {
      "Conditions" => @conditions.map { |c| [c.name, "Condition_#{c.id}"] },
      "Combinaisons Existantes" => @logical_nodes_selectable.map { |ln| [ln.name, "LogicalNode_#{ln.id}"] }
    }
  %>

  <%# ----- Élément Gauche ----- %>
  <div class="form-group">
    <%= form.label :left_element_identifier, "Élément de Gauche" %>
    <%= select_tag "logical_node[left_element_identifier]",
                   grouped_options_for_select(grouped_element_options, selected_left_value),
                   { include_blank: "Sélectionner...", class: "form-control" } %>
  </div>

  <%# ----- Élément Droit ----- %>
  <div class="form-group" data-logical-node-form-target="rightElementSection">
    <%= form.label :right_element_identifier, "Élément de Droite" %>
    <%= select_tag "logical_node[right_element_identifier]",
                   grouped_options_for_select(grouped_element_options, selected_right_value),
                   { include_blank: "Sélectionner...", class: "form-control" } %>
  </div>

  <div class="actions">
    <%= form.submit "Enregistrer", class: "btn btn-primary" %>
  </div>
<% end %>
