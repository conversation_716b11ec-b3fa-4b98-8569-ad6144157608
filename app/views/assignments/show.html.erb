<div class="page-banner">
  <div class="page-banner__left">
    <div class="page-banner__action danger">
      <%= link_to site_assignments_path(@site, category: @category) do %>
        <i class="fas fa-arrow-left action-icon"></i>
        <span class="action-text">Retour</span>
      <% end %>
    </div>

    <div class="stat-item dashboard-card">
      <span><%= @assignment.role.name %></span>
      <span class="stat-label">Rôle</span>
    </div>

    <div class="stat-item page-banner__indicator--<%= @assignment.valid_at? ? "success" : "danger" %>">
      <span class="stat-value fw-bold"><%= @assignment.valid_at? ? "Accès validé" : "Accès refusé" %></span>
    </div>

  <div id="assignment_start_date" class="stat-item dashboard-card">
    <%= link_to edit_start_date_site_assignment_path(@site, @assignment),
                data: { turbo_stream: true },
                class: "date-edit-link" do %>
      <span><%= @assignment.start_date ? @assignment.start_date.strftime("%d/%m/%Y") : "Non précisé" %></span>
      <span class="stat-label">Date de début</span>
    <% end %>
  </div>

    <div class="stat-item dashboard-card">
      <span class="stat-number"><%= @assignment.participants.count %></span>
      <span class="stat-label">Intervenants</span>
    </div>

  <% if @assignment.referents.any? %>
      <div class="stat-item dashboard-card">
        <span class="stat-item"><%= @assignment.referents.map(&:full_name).join(", ") %></span>
        <span class="stat-label">Référents</span>
      </div>
  <% end %>
  </div>
  <div class="page-banner__right">
    <% if permission?(:manage_site_structure) %>
      <div class="page-banner__action">
        <%= link_to manage_lots_site_assignment_path(@site, @assignment, redirect_to: 'show'), data: { turbo_stream: true } do %>
          <i class="fas fa-tasks action-icon"></i>
          <span class="action-text">Lots</span>
        <% end %>
      </div>
    <% end %>
    <% if permission?([:add_assignments, :add_direct_assignments], :or) %>
      <div class="page-banner__action">
        <%= link_to new_site_assignment_path(@site, assignment: { assignment_parent_id: @assignment.id }), data: { turbo_stream: true } do %>
          <i class="fas fa-plus action-icon"></i>
          <span class="action-text">Sous-traitant</span>
        <% end %>
      </div>
    <% end %>
    <% if permission?(:manage_assignments) %>
      <% if @assignment.pending? %>
        <div class="page-banner__action success">
          <%= link_to site_assignment_path(@site, @assignment, assignment: { pending: false }), data: { turbo_method: :put } do %>
            <i class="fas fa-check action-icon"></i>
            <span class="action-text">Valider</span>
          <% end %>
        </div>
        <div class="page-banner__action danger">
          <%= link_to site_assignment_path(@site, @assignment), data: { turbo_method: :delete, turbo_confirm: "Êtes-vous sûr de vouloir refuser cet entreprise ?" } do %>
            <i class="fas fa-times action-icon"></i>
            <span class="action-text">Refuser</span>
          <% end %>
        </div>
      <% else %>
        <div class="page-banner__action orange">
          <%= link_to site_assignment_path(@site, @assignment, assignment: { pending: true }), data: { turbo_method: :put } do %>
            <span class="action-text">Remettre en attente</span>
          <% end %>
        </div>
      <% end %>
    <% end %>
  </div>
</div>

<div class="row" style="<%= browser.device.mobile? ? '' : 'width: 100%' %>">
  <div class="col-md-6">
    <div class="dashboard-card profile-card" style="margin-top: 20px;">
      <div class="profile-card__content <%= browser.device.mobile? ? 'mobile' : '' %>">
        <!-- Logo -->
        <div class="profile-card__photo">
          <%= form_with model: @assignment.company,
                data: {
                  controller: permission?(:manage_assignments) ? "auto-submit" : "",
                  turbo: false
                } do |f| %>
            <%= render 'shared/profile_image',
                  model: @assignment.company,
                  model_name: 'company',
                  attribute_name: 'logo',
                  preview_only: false,
                  can_edit: permission?(:manage_assignments),
                  size: '200px',
                  icon_class: 'fa-building',
                  object_fit: 'contain',
                  update_url: update_image_company_path(@assignment.company),
                  form: f %>
          <% end %>
        </div>
        <!-- Infos -->
        <div class="profile-card__info">
          <div class="profile-card__name">
            <span class="last-name"><%= @assignment.company.name %></span>
          </div>

          <div class="profile-card__details">
            <div class="profile-card__field">
              <i class="fa-solid fa-building"></i>
              <span class="label">Numéro d'entreprise :</span>
              <span class="value"><%= @assignment.company.company_number %></span>
            </div>

            <div class="profile-card__field">
              <i class="fa-solid fa-location-dot"></i>
              <span class="label">Siège social :</span>
              <span class="value"><%= @assignment.company.address %></span>
            </div>

            <div class="profile-card__field">
              <i class="fa-solid fa-user-tag"></i>
              <span class="label">Rôle sur site :</span>
              <% if permission?(:manage_assignments) %>
                <%= form_with model: [@site, @assignment],
                      data: { controller: "auto-submit" } do |f| %>
                  <%= f.select :assignment_role_site_id,
                        @site.assignment_role_sites.map { |ars|
                          [ars.assignment_role.name, ars.id]
                        },
                        { include_blank: false },
                        class: "form-select",
                        data: {
                          action: "change->auto-submit#submit"
                        } %>
                <% end %>
              <% else %>
                <span class="value indicator">
                  <%= @assignment.assignment_role_site.assignment_role.name %>
                </span>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="col-md-6">
    <%= render "subcontractor_info", assignment: @assignment %>
  </div>
</div>

<div class="row">
  <div class="<%= permission?(:manage_documents) ? "col-md-8" : "col-md-12" %>">
    <div id="documents-conditions-status-<%= @assignment.id %>" data-form-auto-submit-target="updateArea" class="dashboard-card dashboard-card__padding" style="margin-top: 20px;">
        <%= render "concerns/verifiables/conditions_status" %>
    </div>
  </div>

  <% if permission?(:manage_documents) %>
    <div class="col-md-4">
      <div id="documents-documents-status-<%= @assignment.id %>" data-form-auto-submit-target="updateArea" class="dashboard-card dashboard-card__padding" style="margin-top: 20px;">
          <%= render "concerns/verifiables/documents_status" %>
      </div>
    </div>
  <% end %>
</div>

<% if permission?(:add_documents) %>
  <div id="documents-upload-area" data-form-auto-submit-target="updateArea" class="dashboard-card dashboard-card__padding" style="margin-top: 20px;">
    <%= render "concerns/verifiables/document_upload_zones", site: @site, needed_document_types: @needed_document_types, documentable: @assignment %>
  </div>
<% end %>

<div id="modal-container"></div>
