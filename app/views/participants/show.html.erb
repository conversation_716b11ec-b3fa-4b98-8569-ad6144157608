<div class="page-banner">
  <div class="page-banner__left">
    <div class="page-banner__action danger">
      <%= link_to site_participants_path(@site, category: (request.referer.present? && request.referer.include?("category=") ? request.referer.split("category=").last.split("&").first : "partner")) do %>
        <i class="fas fa-arrow-left action-icon"></i>
        <span class="action-text">Retour</span>
      <% end %>
    </div>

    <div class="stat-item dashboard-card">
      <span class="stat-label"><%= @participant.full_name %></span>
    </div>

    <div class="stat-item dashboard-card">
      <span class="stat-label"><%= @site.name %></span>
      <% on_site = @participant.currently_on_site? %>
      <% if on_site %>
        <span class="indicator indicator--success m-0">Sur site</span>
      <% end %>
    </div>

    <div class="stat-item page-banner__indicator--<%= @participant.has_access? ? "success" : "danger" %>">
      <span class="stat-value fw-bold"><%= @participant.has_access? ? "Accès validé" : "Accès refusé" %></span>
      <% if @participant.access_granted_by_bypass? && permission?(:view_bypasses) %>
        <span class="stat-label">Par dérogation</span>
      <% elsif @participant.access_denied_by_bypass? && permission?(:view_bypasses) %>
        <span class="stat-label">Par dérogation</span>
      <% end %>
    </div>
  </div>
  <div class="page-banner__right">
    <% if @current_user_site.permission?(:manage_participants) && !@participant.valid_at? %>
      <div class="page-banner__action">
        <%= link_to site_invalid_participant_email_path(@site, @participant), data: { turbo_method: :post } do %>
          <i class="fas fa-plus action-icon"></i>
          <span class="action-text">Mail de relance</span>
        <% end %>
      </div>
    <% end %>

    <% if @current_user_site.permission?(:sync_participants) %>
      <div class="page-banner__action">
        <%= link_to sync_with_external_service_site_participant_path(@site, @participant), data: { turbo_method: :post } do %>
          <i class="fas fa-sync-alt action-icon"></i>
          <span class="action-text">Synchro.</span>
        <% end %>
      </div>
    <% end %>
    <% if @current_user_site.permission?(:manage_visual_badge_requests) %>
      <div class="page-banner__action">
        <%= link_to site_visual_badge_requests_path(@site, ids_list: [@participant.id]), data: { turbo_method: :post } do %>
          <i class="fas fa-id-card action-icon"></i>
          <span class="action-text">Badge</span>
        <% end %>
      </div>
    <% end %>
  </div>
</div>

<div class="row" style="<%= browser.device.mobile? ? '' : 'width: 100%' %>">
  <div class="col-md-5 <%= permission?(:view_bypasses) ? "col-md-5" : "col-md-8" %>">
    <div class="dashboard-card profile-card">
      <div class="profile-card__content <%= 'mobile' if browser.device.mobile? %>">

        <!-- Photo -->
        <div class="profile-card__photo">
          <%= form_with model: [@site, @participant],
                data: {
                  controller: (@participant.new_record? ? "role-company" : "auto-submit"),
                  turbo: (@participant.new_record? ? false : true)
                } do |f| %>
            <%= render 'shared/profile_image',
                  model: @participant,
                  model_name: 'participant',
                  attribute_name: 'photo',
                  preview_only: @participant.new_record?,
                  can_edit: permission?(:manage_participants),
                  update_url: @participant.new_record? ? nil : update_image_site_participant_path(@site, @participant),
                  form: f %>
          <% end %>
        </div>

        <!-- Infos -->
        <div class="profile-card__info">
          <div class="profile-card__name">
            <span class="last-name"><%= @participant.last_name %></span>
            <span class="first-name"><%= @participant.first_name %></span>
          </div>

          <div class="profile-card__details">
            <div class="profile-card__field">
              <i class="fa-solid fa-user-tag"></i>
              <span class="label">Rôle :</span>
              <% if permission?(:manage_participants) %>
                <%= render "role_site_form", participant: @participant, site: @site %>
              <% else %>
                <%= participant_role_indicator(_, @participant) %>
              <% end %>
            </div>

            <div class="profile-card__field">
              <i class="fa-solid fa-building"></i>
              <span class="label">Employeur :</span>
              <% if current_user.admin? %>
                <%= render "people/person_company_form",
                          person: @participant.person,
                          company: @participant.person.company,
                          site: @site %>
              <% else %>
                <span class="value"><%= @participant.employer_name %></span>
              <% end %>
            </div>
          </div>

          <% unless @participant.participant_role&.same_company %>
            <div class="profile-card__field">
              <i class="fa-solid fa-briefcase"></i>
              <span class="label">Travaille pour :</span>
              <% if permission?(:manage_participants) %>
                <%= render "assignment_form", participant: @participant, site: @site %>
              <% else %>
                <span class="value"><%= @participant.assignment_company_name %></span>
              <% end %>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <% if permission?(:view_bypasses) %>
    <div class="col-md-3">
      <div class="dashboard-card dashboard-card__padding" style="margin-top: 20px;">
        <div class="d-flex justify-content-between">
          <div class="dashboard-card__title-container">
            <h5 class="dashboard-card__title">Dérogation</h5>
          </div>
          <%= link_to "Historique", site_bypasses_path(@site, @participant), data: { turbo_stream: true }, class: "mashe-link mashe-link__italic mt-3" %>
        </div>

        <% if @participant.access_granted_by_bypass? || @participant.access_denied_by_bypass? %>
          <%= render "bypasses/bypass_card", bypass: @participant.active_bypass %>
        <% else %>
          <p class="text-muted my-3">Aucune dérogation</p>
          <% if permission?(:manage_bypasses) %>
            <%= link_to "Accorder un accès", new_site_bypass_path(@site, @participant, bypass_type: "allowed"), data: { turbo_stream: true }, class: "mashe-button mashe-button--primary rounded-4 p-2 #{browser.device.mobile? ? 'my-1' : ''}" %>
            <%= link_to "Interdire un accès", new_site_bypass_path(@site, @participant, bypass_type: "denied"), data: { turbo_stream: true }, class: "mashe-button mashe-button--danger rounded-4 p-2 #{browser.device.mobile? ? 'my-1' : ''}" %>
          <% end %>
        <% end %>
      </div>
    </div>
  <% end %>
  <div class="col-md-4">
    <div class="dashboard-card dashboard-card__padding" style="margin-top: 20px;">
      <div class="dashboard-card__title-container">
        <h5 class="dashboard-card__title">Contrôle documentaire</h5>
      </div>
      <% vetting_process = @participant.vetting_process %>
      <% if vetting_process.nil? %>
        <p class="text-muted my-3">Aucun contrôle de vetting disponible.</p>
        <%# Optionnel: Bouton pour lancer un contrôle manuel si nécessaire %>
        <%#= link_to "Lancer un contrôle", trigger_vetting_site_participant_path(@site, @participant), method: :post, class: "mashe-button mashe-button--secondary" %>
      <% else %>
        <% case vetting_process.status %>
        <% when "pending" %>
          <span class="indicator indicator--warning mb-2">Contrôle en attente</span>
          <p class="mt-2">Résultat préliminaire:
            <% if vetting_process.is_favorable %>
              <span class="indicator indicator--success">Favorable</span>
            <% else %>
              <span class="indicator indicator--danger">Défavorable</span>
            <% end %>
          </p>

          <% if vetting_process.vetting_rule_results.any? %>
            <% results = vetting_process.vetting_rule_results %>
            <% passed_count = results.count(&:passed?) %>
            <% failed_count = results.count(&:failed?) %>
            <% skipped_count = results.count(&:skipped?) %>

            <%# Bloc Stimulus - Renamed controller, ensure parent has position: relative if needed %>
            <%# Adding position-relative here directly %>
            <div class="mt-3 position-relative" data-controller="tooltip">
              <%# Résumé - Renamed actions/controller %>
              <div class="vetting-summary"
                   data-action="mouseover->tooltip#show mouseout->tooltip#hide">
                <h6 class="d-inline-block me-2 mb-0 align-middle">Détails des règles :</h6>
                <% if passed_count > 0 %><span class="indicator indicator--success indicator--sm me-1"><i class="fas fa-check me-1"></i> <%= passed_count %></span><% end %>
                <% if failed_count > 0 %><span class="indicator indicator--danger indicator--sm me-1"><i class="fas fa-times me-1"></i> <%= failed_count %></span><% end %>
                <% if skipped_count > 0 %><span class="indicator indicator--neutral indicator--sm"><i class="fas fa-forward me-1"></i> <%= skipped_count %></span><% end %>
                <i class="fas fa-info-circle text-muted ms-1 align-middle"></i>
              </div>

              <%# Tooltip content - Renamed target, applied styles from _documents_status %>
              <div class="position-absolute bg-white shadow p-2 rounded d-none"
                   data-tooltip-target="content"
                   style="top: -10px; transform: translateY(-100%); right: 0; z-index: 100; min-width: 300px; border: 1px solid #dee2e6;">
                 <div class="errors-title border-bottom pb-1 mb-2 d-flex justify-content-between">
                    <strong>Détails des règles</strong>
                    <i class="fas fa-list-ul"></i> <%# Changed icon slightly %>
                 </div>
                 <ul class="list-unstyled mb-0">
                   <% results.each do |result| %>
                     <li class="mb-1">
                       <span class=""><%= result.parameter.try(:vetting_rule).try(:description) || 'Règle inconnue' %>:</span>
                       <% case result.result %>
                       <% when "passed" %>
                        <span class="indicator indicator--success indicator--sm ms-1">Réussi</span>
                       <% when "failed" %>
                         <span class="indicator indicator--danger indicator--sm ms-1">Échoué</span>
                       <% when "skipped" %>
                       <span class="indicator indicator--neutral indicator--sm ms-1">Ignoré</span>
                       <% end %>
                     </li>
                   <% end %>
                 </ul>
              </div>
            </div>
          <% end %>

          <div class="mt-3">
            <%= link_to "Contrôler", edit_site_vetting_process_path(@site, vetting_process), class: "mashe-button mashe-button--primary rounded-4 p-2" %>
          </div>

        <% when "approved" %>
          <span class="indicator indicator--success mb-2">Contrôle Approuvé</span>
          <p class="mt-2 mb-0">
            Validé le <%= l(vetting_process.validated_at, format: :long) %>
            <% if vetting_process.validator %>
              par <%= vetting_process.validator.try(:full_name) || vetting_process.validator.try(:email) %>
            <% end %>
          </p>
        <% when "rejected" %>
          <span class="indicator indicator--danger mb-2">Contrôle Rejeté</span>
           <p class="mt-2 mb-0">
            Rejeté le <%= l(vetting_process.validated_at, format: :long) %>
            <% if vetting_process.validator %>
              par <%= vetting_process.validator.try(:full_name) || vetting_process.validator.try(:email) %>
            <% end %>
          </p>
        <% end %>
      <% end %>
    </div>
  </div>
</div>


<div class="row" style="<%= browser.device.mobile? ? '' : 'width: 100%' %>">
  <div class="<%= permission?(:manage_documents) ? "col-md-8" : "col-md-12" %>">
    <div id="documents-conditions-status-<%= @participant.id %>" data-form-auto-submit-target="updateArea" class="dashboard-card dashboard-card__padding" style="margin-top: 20px;">
        <%= render "concerns/verifiables/conditions_status" %>
    </div>
  </div>

  <% if permission?(:manage_documents) %>
    <div class="col-md-4">
      <div id="documents-documents-status-<%= @participant.id %>" data-form-auto-submit-target="updateArea" class="dashboard-card dashboard-card__padding" style="margin-top: 20px">
          <%= render "concerns/verifiables/documents_status" %>
      </div>
    </div>
  <% end %>
</div>
<% if permission?(:add_documents) %>
  <div class="row" style="<%= browser.device.mobile? ? '' : 'width: 100%' %>">
    <div class="col-md-12">
      <div id="documents-upload-area-<%= @participant.id %>" data-form-auto-submit-target="updateArea" class="dashboard-card dashboard-card__padding" style="margin-top: 20px;">
        <%= render "concerns/verifiables/document_upload_zones", site: @site, needed_document_types: @needed_document_types, documentable: @participant %>
      </div>
    </div>
  </div>
<% end %>
