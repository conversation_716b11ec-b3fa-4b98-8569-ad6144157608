<% request_origin = local_assigns[:request_origin] || "participant" %>
<% document = local_assigns[:document] || nil %>
<% from = determine_request_source(request.referer) %>
<% scope = determine_uploads_index_scope(request) %>
<% status = determine_uploads_index_status(request) %>

<%= form_with model: [@site, participant],
              multipart: true,
              url: request_origin == "document" ? update_documentable_site_document_path(@site, document, from:, scope:, status:) : site_participants_path(@site),
              method: request_origin == "document" ? :patch : :post,
              data: {
                controller: "role-company",
                role_company_assignment_role_sites_api_url_value: by_assignment_api_participant_role_sites_path(site_id: @site.id),
                role_company_current_participant_role_site_id_value: @current_participant_role_site&.id,
                role_company_same_company_roles_value: @site.participant_role_sites.select(&:same_company).map(&:id),
                turbo: request_origin == "document" ? true : false
              } do |f| %>

  <div class="modal-body">
    <%# Affiche les erreurs globales du formulaire %>
    <% if f.object.errors.any? %>
      <div class="alert alert-danger">
        <h5>Erreurs :</h5>
        <ul>
          <% f.object.errors.full_messages.each do |message| %>
            <li><%= message %></li>
          <% end %>
        </ul>
      </div>
    <% end %>

    <div class="row">
      <!-- Colonne de gauche pour la photo (preview seulement à la création) -->
      <% if photo_form %>
        <div class="col-md-3">
          <%= render 'shared/profile_image',
                    model: participant,
                    model_name: 'participant',
                    attribute_name: 'photo',
                    preview_only: participant.new_record?, # Preview only pour les nouveaux participants
                    update_url: participant.new_record? ? nil : update_image_site_participant_path(@site, participant),
                    form: f %>
        </div>
      <% end %>
      <!-- Colonne de droite pour les informations -->
      <div class="<%= @photo_form ? "col-md-9" : "col-md-12" %>">
        <%# Utilisation de fields_for pour les attributs de la personne associée %>
        <%= f.fields_for :person, participant.person || Person.new do |person_fields| %>
          <div class="row mb-3">
            <div class="col-md-6">
              <div class="form-group">
                <%= person_fields.label :first_name, "Prénom", class: "form-label" %>
                <%= person_fields.text_field :first_name, class: "form-control", required: true %>
                <%= render "shared/field_errors", object: person_fields.object, field: :first_name %>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <%= person_fields.label :last_name, "Nom", class: "form-label" %>
                <%= person_fields.text_field :last_name, class: "form-control", required: true %>
                <%= render "shared/field_errors", object: person_fields.object, field: :last_name %>
              </div>
            </div>
          </div>
          <div class="row mb-3">
             <div class="col-md-6">
                <div class="form-group">
                   <%= person_fields.label :birth_date, "Date de naissance", class: "form-label" %>
                   <%= person_fields.text_field :birth_date, class: "form-control", data: { controller: "datepicker" }, required: true %>
                   <%= render "shared/field_errors", object: person_fields.object, field: :birth_date %>
                </div>
             </div>
            <div class="col-md-6">
              <div class="form-group">
                <%= f.label :assignment_id, "Entreprise utilisatrice", class: "form-label" %>
                <%= f.select :assignment_id,
                    options_for_select(
                      @assignments.map { |a| [a.company_name, a.id, { 'data-company-id': a.company_id }] },
                      participant.assignment_id
                    ),
                    { prompt: "Sélectionnez une entreprise" },
                    {
                      class: "form-control",
                      data: {
                        role_company_target: "assignmentSelect",
                        action: "change->role-company#assignmentChanged " + (participant.new_record? ? "" : "change->auto-submit#submit")
                      }
                    } %>
                <%= render "shared/field_errors", object: participant, field: :assignment_id %>
              </div>
            </div>

          </div>
        <%# Fin de fields_for :person %>

        <div class="row mb-3">
          <div class="col-md-6">
            <div class="form-group">
              <%= f.label :participant_role_site_id, "Rôle sur site", class: "form-label" %>
              <%= f.collection_select :participant_role_site_id,
                  [], # On commence avec un tableau vide
                  :id,
                  :name,
                  { prompt: "Sélectionnez un rôle" },
                  class: "form-control",
                  required: true,
                  disabled: true,
                  data: {
                    role_company_target: "roleSelect",
                    action: "change->role-company#roleChanged"
                  } %>
              <%= render "shared/field_errors", object: f.object, field: :participant_role_site_id %>
            </div>
          </div>

          <div class="col-md-6">
            <div class="form-group">
              <%# Champ caché pour l'employeur, géré par le contrôleur role-company si nécessaire %>
              <%# Ou sélection manuelle si la logique est différente %>
              <%# Note: Le modèle Participant gère l'employeur via la Person. %>
              <%# Le champ 'employer_id' dans les params est un alias pour person.company_id dans le contrôleur. %>
              <%= person_fields.label :company_id, "Employeur", class: "form-label" %>
              <%= person_fields.collection_select :company_id,
                  @employers,
                  :id,
                  :name,
                  { prompt: "Sélectionnez un employeur", selected: "" },
                  class: "form-control",
                  required: true,
                  data: {
                    role_company_target: "companySelect",
                  } %>
              <%= render "shared/field_errors", object: person_fields.object, field: :company_id %>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="modal-footer">
    <%= f.submit "Créer intervenant", class: "mashe-button mashe-button--primary" %>
  </div>
<% end %>
