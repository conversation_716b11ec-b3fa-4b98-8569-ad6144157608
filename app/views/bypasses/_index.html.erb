<%
  dataset = DataTables::MasheDataTable.new()
  dataset.title = "Historique des dérogations"
  dataset.id = "bypasses-index-datatable"
  dataset.rows = bypasses
  dataset.searchable = false
  dataset.pagination = { initial_per_page: 5, per_page_options: [5, 10, 25, 50] }
  dataset.columns = [
    {
      header: "Date",
      formatter: :date,
      method: :created_at,
      sortable: true,
      classes: ["col-1", "col-lg-1"]
    },
    {
      header: "Type",
      method: :bypass_type,
      formatter: :helper,
      format_params: [:bypass_type_indicator],
      type: :indicator,
      sortable: false,
      classes: ["col-2", "col-lg-2"]
    },
    {
      header: "Créé par",
      method: :created_by,
      formatter: :helper,
      format_params: [:full_name],
      sortable: false,
      classes: ["col-2", "col-lg-2"]
    },
    {
      header: "Raison",
      method: :reason,
      sortable: false,
      classes: ["col-7", "col-lg-7", "text-start", "justify-content-start", "text-wrap"]
    }
  ]

  dataset.mobile_row_type = "card"
  dataset.mobile_card_header = [
   {
      header: "Date",
      formatter: :date,
      method: :created_at,
      sortable: true,
      classes: ["col-1", "col-lg-1"]
    },
    {
      header: "Type",
      method: :bypass_type,
      formatter: :helper,
      format_params: [:bypass_type_indicator],
      type: :indicator,
      sortable: false,
      classes: ["col-2", "col-lg-2"]
    }
  ]
  dataset.mobile_card_body = [
    {
      header: "Créé par",
      method: :created_by,
      formatter: :helper,
      format_params: [:full_name],
    },
    {
      header: "Raison",
      method: :reason,
      sortable: false,
      classes: ["col-7", "col-lg-7", "text-start", "justify-content-start", "text-wrap"]
    }
  ]
  dataset.build_hash
%>

<%= render partial: "shared/data_table/data_table_wrapper", locals: { dataset: dataset } %>
