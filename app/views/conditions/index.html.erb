<div class="page-banner">
  <div class="page-banner__left">
    <%= link_to new_site_condition_path(@site), class: "page-banner__action", data: { turbo_stream: true } do %>
      <i class="fas fa-plus action-icon"></i>
      <span class="action-text">Condition</span>
    <% end %>

    <%= link_to new_site_logical_node_path(@site), class: "page-banner__action", data: { turbo_stream: true } do %>
      <i class="fas fa-plus action-icon"></i>
      <span class="action-text">Combinaison logique</span>
    <% end %>

    <%= link_to site_update_validity_status_path(@site), class: "page-banner__action", data: { turbo_stream: true, turbo_confirm: "Êtes-vous sûr de vouloir mettre à jour les statuts de validité de tous les accès de ce site ? Cela peut prendre du temps et sera réalisé en arrière-plan." } do %>
      <i class="fas fa-check action-icon"></i>
      <span class="action-text">Met<PERSON> à jour</span>
    <% end %>

    <%= link_to new_site_condition_group_path(@site), class: "page-banner__action", data: { turbo_stream: true } do %>
      <i class="fas fa-layer-group action-icon"></i>
      <span class="action-text">Groupe</span>
    <% end %>
  </div>
</div>

<div class="row">
  <div class="col-lg-6">
    <div class="dashboard-card" style="margin-top: 20px;" id="participant-conditions-list">
      <%= render "conditions/datatables/participant_conditions", conditions: @conditions %>
    </div>
  </div>
  <div class="col-lg-6">
    <div class="dashboard-card" style="margin-top: 20px;" id="company-conditions-list">
      <%= render "conditions/datatables/company_conditions", conditions: @conditions %>
    </div>
  </div>
</div>

<div class="row" style="margin-top: 20px;">
  <div class="col-lg-12">
    <div class="dashboard-card" id="logical-nodes-list">
      <%# Assurez-vous que @logical_nodes est chargé dans votre contrôleur %>
      <%= render "conditions/datatables/logical_nodes", logical_nodes: @logical_nodes %>
    </div>
  </div>
</div>

<div class="row" style="margin-top: 20px;">
  <div class="col-lg-12">
    <div class="dashboard-card" id="condition-groups-list">
      <%= render "conditions/datatables/condition_groups", condition_groups: @condition_groups %>
    </div>
  </div>
</div>
