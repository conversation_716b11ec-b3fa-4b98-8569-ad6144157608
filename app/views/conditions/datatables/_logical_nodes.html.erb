<%# app/views/conditions/datatables/_logical_nodes.html.erb %>
<%
  dataset = DataTables::MasheDataTable.new()
  dataset.title = "Combinaisons de conditions" # Ou "Nœuds Logiques"
  dataset.id = "logical-nodes-datatable"
  dataset.rows = logical_nodes # Variable passée au partial
  dataset.searchable = true
  dataset.pagination = { initial_per_page: 5, per_page_options: [5, 10, 25] }
  dataset.checkable = {method: :id} # Si une action groupée est envisagée plus tard
  dataset.columns = [
    { header: "Nom", method: :name, sortable: true, classes: ["col-lg-3", "justify-content-start"] },
    {
      header: "Élément Gauche",
      method: :left_element_display_name, # Utilise la méthode suggérée
      sortable: false, # Trier sur un nom d'association polymorphe peut être complexe
      classes: ["col-lg-3"]
    },
    {
      header: "Opérateur",
      method: :operator_display, # Utilise la méthode suggérée dans le modèle LogicalNode
      sortable: true,
      classes: ["col-lg-2"]
    },
    {
      header: "Élément Droit",
      method: :right_element_display_name, # Utilise la méthode suggérée
      sortable: false,
      classes: ["col-lg-3"]
    }
  ]
  dataset.actions = [
    {
      link: {
        content: "Modifier",
        path: :edit_site_logical_node_path, # Assurez-vous que cette route existe ou sera créée
        args: [@site, "self"], # "self" sera l'instance du logical_node
        turbo_stream: true # Ou false selon le comportement souhaité
      }
    },
    {
      link: {
        content: "Supprimer",
        path: :site_logical_node_path, # Assurez-vous que cette route existe ou sera créée
        args: [@site, "self"],
        turbo_method: :delete,
        turbo_stream: true,
        turbo_confirm: "Êtes-vous sûr de vouloir supprimer cette combinaison ?"
      }
    }
  ]
  dataset.build_hash
%>

<%= render partial: "shared/data_table/data_table_wrapper", locals: { dataset: dataset } %>
