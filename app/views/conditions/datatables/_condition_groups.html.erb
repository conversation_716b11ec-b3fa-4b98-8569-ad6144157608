<%
  dataset = DataTables::MasheDataTable.new()
  dataset.title = "Groupes de conditions"
  dataset.id = "condition-groups-datatable"
  dataset.rows = condition_groups
  dataset.searchable = true
  dataset.pagination = { initial_per_page: 10, per_page_options: [10, 25, 50] }
  dataset.columns = [
    {
      header: "Nom",
      method: :humanized_name,
      sortable: true,
      classes: ["col-lg-2", "justify-content-start"]
    },
    {
      header: "Identifiant",
      method: :name,
      sortable: false,
      classes: ["col-lg-1"]
    },
    {
      header: "Éléments (Conditions/Nœuds)",
      method: :all_element_names,
      formatter: :indicators,
      sortable: false,
      classes: ["col-lg-6 flex-wrap"]
    },
    {
      header: "Type de cible",
      method: :target_type_humanized,
      sortable: true,
      classes: ["col-lg-2"]
    }
  ]
  dataset.actions = [
    {
      link: {
        content: "Gérer les éléments",
        path: :manage_site_condition_group_path,
        args: [@site, "self"],
        turbo_stream: true
      }
    },
    {
      link: {
        content: "Modifier",
        path: :edit_site_condition_group_path,
        args: [@site, "self"],
        turbo_stream: true
      }
    },
    {
      link: {
        content: "Supprimer",
        path: :site_condition_group_path,
        args: [@site, "self"],
        turbo_method: :delete,
        turbo_stream: true,
        turbo_confirm: "Êtes-vous sûr de vouloir supprimer ce groupe ?"
      }
    }
  ]
  dataset.build_hash
%>

<%= render partial: "shared/data_table/data_table_wrapper", locals: { dataset: dataset } %>
