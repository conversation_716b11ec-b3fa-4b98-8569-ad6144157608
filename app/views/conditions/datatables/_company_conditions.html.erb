<%
  dataset = DataTables::MasheDataTable.new()
  dataset.title = "Conditions des entreprises"
  dataset.id = "company-conditions-datatable"
  dataset.rows = conditions.where(target_type: :company).or(conditions.where(target_type: :assignment))
  dataset.searchable = true
  dataset.pagination = { initial_per_page: 5, per_page_options: [5, 10, 25] }
  dataset.checkable = {method: :id}
  dataset.columns = [
    { header: "Nom", method: :name, sortable: true, classes: ["col-lg-6", "justify-content-start"] },
    {
      header: "Groupes",
      method: :condition_group_names,
      formatter: :indicators,
      sortable: false,
      classes: ["col-lg-3"]
    }
  ]
  dataset.actions = [
    {
      link: {
        content: "Modifier",
        path: :edit_site_condition_path,
        args: [@site, "self"],
        turbo_stream: true
      }
    },
    {
      link: {
        content: "Supprimer",
        path: :site_condition_path,
        args: [@site, "self"],
        turbo_method: :delete,
        turbo_stream: true,
        turbo_confirm: "Êtes-vous sûr ?"
      }
    }
  ]
  dataset.build_hash
%>

<%= render partial: "shared/data_table/data_table_wrapper", locals: { dataset: dataset } %>
