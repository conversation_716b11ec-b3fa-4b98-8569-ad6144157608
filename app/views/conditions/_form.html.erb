<%= simple_form_for [@site, @condition] do |f| %>
  <%= render "shared/form_errors", resource: @condition %>
  <%= f.input :name,
      label: "Nom",
      input_html: { class: "form-control" } %>

  <%= f.input :description,
      as: :text,
      input_html: { class: "form-control" } %>

  <%= f.input :fallback_message,
      label: "Message d'erreur",
      input_html: { class: "form-control" } %>

  <div>
    <%= f.input :target_type,
        label: "Type de cible",
        collection: Condition.target_types.keys.map { |k| [k.humanize, k] },
        as: :radio_buttons,
        input_html: { class: "form-check-input" } %>

    <%= f.input :purpose,
        label: "Objectif",
        collection: Condition.purposes.keys.map { |k| [k.humanize, k] },
        as: :radio_buttons,
        input_html: { class: "form-check-input" } %>

    <%= f.input :global,
        label: "Condition globale ?",
        as: :boolean,
        input_html: { class: "form-check-input" } %>
  </div>

  <div data-controller="condition-parameters"
      data-condition-parameters-config-value="<%= ConditionConfiguration::CONDITION_TYPE_PARAMETERS.to_json %>"
      data-condition-parameters-existing-parameters-value="<%= (@condition.parameters || {}).to_json %>">

    <%= f.input :condition_type,
        label: "Type de condition",
        collection: Condition::HUMANIZED_CONDITION_TYPES.map { |k, v| [v, k] },
        input_html: {
          class: "form-select",
          data: { action: "change->condition-parameters#updateParameterFields" }
        } %>

    <div data-condition-parameters-target="parametersContainer">
      <%# Les champs de paramètres seront générés ici dynamiquement %>
    </div>
  </div>

  <script>
    window.currentSiteId = <%= @site.id %>;
  </script>

  <div class="form-actions">
    <%= f.button :submit, "Enregistrer", class: "btn btn-primary" %>
  </div>
<% end %>
