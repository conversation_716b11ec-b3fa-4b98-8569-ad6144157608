<%= turbo_stream.update "modal-container" do %>
  <div class="mashe-modal" data-controller="mashe-modal">
    <div class="mashe-modal__overlay" data-action="click->mashe-modal#close"></div>
    <div class="mashe-modal__content">
      <button class="mashe-modal__close" data-action="click->mashe-modal#close">&times;</button>
      <div class="mashe-modal__body">
        <%= simple_form_for [:admin, @site], url: admin_site_path(@site), method: :patch, data: { turbo: true } do |f| %>
          <div class="modal-header">
            <h5 class="modal-title">Modifier le chantier</h5>
          </div>

          <div class="modal-body">
            <div class="row mb-4">
              <div class="col-md-6">
                <%= f.input :name,
                            label: "Nom du chantier",
                            placeholder: "Chantier de la rue de la paix",
                            required: true %>
              </div>
              <div class="col-md-6">
                <%= f.input :short_name,
                            label: "Nom de code",
                            placeholder: "Exemple: RDP",
                            required: true %>
              </div>
            </div>

            <div class="row mb-4">
              <div class="col-md-6" data-controller="address-confirmation" data-address-confirmation-access-token-value="<%= ENV.fetch("MAPBOX_ACCESS_TOKEN") %>">
                <%= f.input :address,
                            label: "Adresse du chantier",
                            placeholder: "123 Rue de la Paix, 75000 Paris",
                            required: true,
                            input_html: { data: { address_confirmation_target: "addressInput" } } %>
                <div class="d-flex align-items-center gap-2">
                  <div class="mashe-button mashe-button--primary rounded-4 p-2 inline-block cursor-pointer mb-2" data-action="click->address-confirmation#confirmAddress">Confirmer l'adresse</div>
                  <div class="address-confirmation-status ms-2 d-none text-success">
                    <i class="fas fa-check-circle"></i> Adresse confirmée
                  </div>
                  <div class="address-error text-danger d-none">
                    <i class="fas fa-exclamation-circle"></i> Impossible de confirmer cette adresse
                  </div>
                </div>
              </div>

              <div class="col-md-6">
                <%= f.simple_fields_for :site_detail do |sd| %>
                  <div class="form-group">
                    <label class="form-label">Email du chantier</label>
                    <div class="d-flex align-items-center">
                      <%= sd.input_field :base_email,
                                        class: "form-control w-50 me-2",
                                        placeholder: "nom-du-chantier",
                                        required: true %>
                      <%= sd.input :skip_validations, as: :hidden, input_html: { value: true } %>
                      <span class="form-control ps-1 text-muted rounded-4">@mashe.fr</span>
                    </div>
                    <% if @site.site_detail&.errors&.include?(:base_email) %>
                      <div class="invalid-feedback d-block">
                        Cet email est déjà utilisé
                      </div>
                    <% end %>
                    <small class="form-text text-muted">
                      Seul le nom avant @mashe.fr doit être renseigné.
                    </small>
                  </div>
                <% end %>
              </div>
            </div>

            <div class="row mb-4">
              <div class="col-md-6">
                <%= f.input :company_id,
                            collection: @companies,
                            label: "Entreprise",
                            prompt: "Sélectionnez une entreprise",
                            required: true,
                            input_html: { class: "form-select" } %>
              </div>
            </div>

            <!-- Détails du site -->
            <h5 class="mb-3">Détails du site</h5>
            
            <%= f.simple_fields_for :site_detail do |sd| %>
              <div class="row mb-4">
                <div class="col-md-6">
                  <%= sd.input :description,
                              label: "Description",
                              as: :text,
                              input_html: { rows: 3 } %>
                </div>
                <div class="col-md-6">
                  <%= sd.input :floor_area,
                              label: "Surface (m²)",
                              as: :integer %>
                </div>
              </div>

              <div class="row mb-4">
                <div class="col-md-6">
                  <%= sd.input :permit_number,
                              label: "Numéro de permis" %>
                </div>
                <div class="col-md-6">
                  <%= sd.input :permit_date,
                              label: "Date de permis",
                              as: :date %>
                </div>
              </div>

              <div class="row mb-4">
                <div class="col-md-6">
                  <%= sd.input :site_image,
                              label: "Image du site",
                              as: :file %>
                </div>
              </div>
            <% end %>

            <h5 class="mb-3">Options</h5>

            <div class="document-fields-manager">
              <% @features.each do |category, features| %>
                <div class="document-type-section mb-4">
                  <h6 class="document-type-title"><%= t("features.categories.#{category}") %></h6>
                  <div class="document-fields-list">
                    <% features.each do |feature| %>
                      <div class="document-field-item form-check">
                        <%= check_box_tag "feature_mappings[#{feature.name}]",
                                        "1",
                                        @current_features.include?(feature.name),
                                        class: "form-check-input",
                                        id: "feature_#{feature.id}" %>
                        <%= label_tag "feature_#{feature.id}",
                                    feature.humanized_name,
                                    class: "form-check-label" %>
                      </div>
                    <% end %>
                  </div>
                </div>
              <% end %>
            </div>
          </div>

          <div class="modal-footer">
            <%= f.submit "Modifier le chantier", class: "mashe-button mashe-button--primary" %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
<% end %> 