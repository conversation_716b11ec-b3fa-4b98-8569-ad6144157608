<%= turbo_stream.update "modal-container" do %>
  <div class="mashe-modal" data-controller="mashe-modal">
    <div class="mashe-modal__overlay" data-action="click->mashe-modal#close"></div>
    <div class="mashe-modal__content" style="width: 1200px; max-width: 95vw;">
      <button class="mashe-modal__close" data-action="click->mashe-modal#close">&times;</button>
      <div class="mashe-modal__body">
        <div class="row">
          <!-- PDF à gauche -->
          <div class="col-md-7">
            <div class="document-preview-container mb-4" style="height: 100%; min-height: 600px; border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden;">
              <%= render partial: "documents/preview", locals: { document: @document }, formats: [:html] %>
            </div>
          </div>

          <!-- Formulaire d'édition à droite -->
          <div class="col-md-5">
            <% if @document_validation_status&.dig(:failed_fields)&.any? %>
              <div class="border border-danger rounded p-3 mb-4 bg-danger-subtle">
                <h6 class="text-danger mb-2">Erreurs de validation détectées :</h6>
                <ul class="mb-0 text-danger">
                  <% @document_validation_status&.dig(:failed_fields)&.each do |error| %>
                    <small><%= error[:message] %></small>
                  <% end %>
                </ul>
              </div>
            <% end %>
            <div id="document-edit-container" class="admin-document-edit-container">
              <div class="extracted-fields document-edit-form" id="document-edit-form">
                <%= simple_form_for @document, url: admin_document_path(@document),
                                 html: { class: "no-bs-validation" },
                                 data: {
                                   controller: "document-edit",
                                   action: "ajax:success->document-edit#handleUpdateSuccess"
                                 } do |f| %>

                  <% if @document.document_type.present? && @document.document_type.document_category == "imported" %>
                    <div class="document-scope" data-document-edit-target="documentScope">
                      <div class="document-scope-selector">
                        <div class="form-check form-check-inline">
                          <input class="form-check-input" type="radio" name="documentScope" id="scopePersonal" value="personal"
                                <%= @document.document_type&.personal_document? ? 'checked' : '' %>
                                data-action="change->document-edit#changeScopeSelection">
                          <label class="form-check-label" for="scopePersonal">
                            Document personnel
                          </label>
                        </div>
                        <div class="form-check form-check-inline">
                          <input class="form-check-input" type="radio" name="documentScope" id="scopeEnterprise" value="enterprise"
                                <%= @document.document_type&.enterprise_document? ? 'checked' : '' %>
                                data-action="change->document-edit#changeScopeSelection">
                          <label class="form-check-label" for="scopeEnterprise">
                            Document d'entreprise
                          </label>
                        </div>
                      </div>
                    </div>

                    <%= f.association :document_type,
                                    collection: DocumentType.for_site(@site).order(:name).map { |dt| [dt.humanized_name, dt.id] },
                                    label: false,
                                    input_html: {
                                      novalidate: true,
                                      class: "form-select tomselect-field",
                                      data: {
                                        document_edit_target: "documentTypeSelect",
                                        action: "change->document-edit#changeDocumentType",
                                        site_id: @site.id,
                                        document_id: @document.id
                                      }
                                    } %>

                  <% else %>
                    <%= f.input :document_type_id, as: :hidden, input_html: { value: @document.document_type.id } %>
                    <h5 class="mb-3">
                      <%= @document.document_type.humanized_name %>
                    </h5>
                  <% end %>

                  <div id="documentable-details" class="mb-3">
                    <%= render partial: "documents/documentable_details", locals: { document: @document }, formats: [:html] %>
                  </div>

                  <% if @document.document_type %>
                    <% represented_model = @document.document_type.represented_model_name %>
                    <% if represented_model.present? %>
                      <div class="document-associations-container mb-3">
                        <%= render "documents/document_associations_section",
                                 document: @document,
                                 represented_model: represented_model %>
                      </div>
                    <% end %>
                  <% end %>

                  <div id="extracted-fields-container" data-document-edit-target="extractedFieldsContainer">
                    <% if @document.document_type.present? %>
                      <div class="extracted-fields-list mt-3">
                        <% @document.document_type.document_fields.each do |field| %>
                          <% is_full_width = %i[list].include?(field.format.to_sym) %>
                          <div class="extracted-field <%= 'full-width' if is_full_width %>">
                            <% extracted_field = @document.extracted_fields.find_or_initialize_by(document_field: field) %>
                            <% unless field.displayed? %>
                              <div class="admin-only-field-indicator">Champ admin uniquement</div>
                            <% end %>
                            <%= simple_fields_for "extracted_fields[#{field.id}]", extracted_field do |ef| %>
                              <%= render_document_field_input(ef, field, extracted_field) %>
                              <%= ef.input :document_field_id, as: :hidden, input_html: { value: field.id } %>
                            <% end %>
                          </div>
                        <% end %>
                      </div>
                    <% end %>
                  </div>

                  <div class="form-actions mt-4">
                    <%= f.button :submit, "Enregistrer les modifications", class: "mashe-button mashe-button--primary" %>
                  </div>
                <% end %>
                <div class="d-flex justify-content-end mt-3">
                  <%= link_to "Supprimer", document_path(id: @document.id, site_id: @site.id),
                              data: { turbo_method: :delete, turbo_confirm: "Êtes-vous sûr de vouloir supprimer ce document ?" },
                              class: "mashe-button--danger rounded-4 px-2 py-1 align-self-center" %>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
<% end %>
