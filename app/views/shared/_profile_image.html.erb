<%# Partial réutilisable pour afficher et gérer une image de profil %>
<%# Paramètres:
  - model: l'instance du modèle (ex: @pending_participant)
  - model_name: le nom du modèle (ex: 'pending_participant')
  - attribute_name: le nom de l'attribut pour l'image (ex: 'photo')
  - preview_only: booléen pour indiquer si c'est en mode prévisualisation uniquement
  - can_edit: booléen pour indiquer si c'est en mode édition
  - update_url: l'URL pour mettre à jour l'image (nil si preview_only est true)
  - form: l'objet form_with pour le champ file_field
  - size: la taille de l'image (ex: '200px')
  - icon_class: la classe de l'icône (ex: 'fas fa-user')
  - object_fit: la valeur de l'attribut object-fit (ex: 'cover')
  - hover_preview: booléen pour indiquer si c'est en mode grand aperçu au survol
%>
<% can_edit = true if can_edit.nil? %>
<% size = size || '200px' %>
<% icon_class = icon_class || 'fa-user' %>
<% object_fit = object_fit || 'cover' %>
<% hover_preview = hover_preview || false %>

<%# Types d'images autorisés pour les participants %>
<% participant_image_types = "image/jpeg,image/jpg,image/png,image/webp" %>
<% general_image_types = "image/*" %>
<% accept_types = (model_name == 'participant' && attribute_name == 'photo') ? participant_image_types : general_image_types %>

<div class="profile-image__container" style="max-height: <%= size %>"
     data-controller="profile-image"
     data-profile-image-model-name-value="<%= model_name %>"
     data-profile-image-attribute-name-value="<%= attribute_name %>"
     <% if preview_only %>
     data-profile-image-preview-only-value="true"
     <% else %>
      data-profile-image-model-id-value="<%= model.id %>"
      data-profile-image-update-url-value="<%= update_url %>"
     <% end %>>

  <!-- Wrapper pour l'image/placeholder avec bouton superposé -->
  <div class="profile-image__wrapper mb-2 position-relative" data-profile-image-target="wrapper" data-controller="<%= hover_preview ? "image-preview" : "" %>" data-image-preview-src-value="<%= model.send(attribute_name).attached? ? url_for(model.send(attribute_name)) : nil %>">
    <% if model.send(attribute_name).attached? %>
      <%= image_tag url_for(model.send(attribute_name)),
                    class: "profile-image__preview",
                    style: "object-fit: #{object_fit};",
                    data: { profile_image_target: "preview" } %>
    <% else %>
      <div class="profile-image__placeholder" data-profile-image-target="placeholder">
        <i class="fas <%= icon_class %> fa-3x text-secondary"></i>
      </div>
    <% end %>

    <!-- Bouton d'upload superposé sur l'image -->
    <% if can_edit %>
      <div class="profile-image__upload-overlay">
        <label class="profile-image__upload-button">
          <i class="fas fa-camera"></i>
        <span><%= model.send(attribute_name).attached? ? "Changer" : "Ajouter" %></span>
        <%= form.file_field attribute_name,
                        accept: accept_types,
                        class: "profile-image__upload-input",
                        direct_upload: true,
                        data: {
                          profile_image_target: "input",
                          action: "change->profile-image#handleFileChange"
                        } %>
        </label>
      </div>
    <% end %>
  </div>
</div>
