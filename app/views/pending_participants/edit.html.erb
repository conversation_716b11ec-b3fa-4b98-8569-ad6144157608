<div class="page-banner">
  <div class="page-banner__left">

    <div class="page-banner__action danger">
      <%= link_to site_pending_participants_path(@site) do %>
        <i class="fas fa-arrow-left action-icon"></i>
        <span class="action-text">Retour</span>
      <% end %>
    </div>

    <div class="stat-item dashboard-card">
      <span class="stat-label"><%= @pending_participant.first_name %> <%= @pending_participant.last_name %></span>
    </div>

    <div class="stat-item dashboard-card">
      <span class="stat-label"><%= @site.name %></span>
    </div>

  </div>

  <div class="page-banner__right">
    <%# Variable pour déterminer si la validation est possible %>
    <% can_validate = @pending_participant.has_completed_informations? || (@site.get_setting("pending_participants", "can_be_validated_if_incomplete") && permission?(:validate_pending_participants) && @pending_participant.has_minimal_informations?) %>

    <% if can_validate %>
      <%# Bouton activé normal %>
      <div class="page-banner__action success">
        <%= link_to convert_site_pending_participant_path(@site, @pending_participant), data: { turbo_method: :patch } do %>
          <i class="fas fa-check action-icon"></i>
          <span class="action-text">Valider</span>
        <% end %>
      </div>
    <% else %>
      <%# Div pour le tooltip, complètement séparée du bouton désactivé %>
      <div data-controller="tooltip" class="position-relative">
        <%# Bouton désactivé qui déclenche le tooltip %>
        <div class="page-banner__action success page-banner__action--disabled"
             data-action="mouseover->tooltip#show mouseout->tooltip#hide">
          <span style="display: flex; flex-direction: column; align-items: center; width: 100%; height: 100%; justify-content: center;">
            <i class="fas fa-check action-icon"></i>
            <span class="action-text">Valider</span>
          </span>
        </div>

        <div class="card tooltip-content d-none p-3 shadow-sm text-dark"
             data-tooltip-target="content"
             style="position: absolute; z-index: 1050; min-width: 250px; right: 0;">
          <h6 class="card-title mb-2">Validation impossible :</h6>
          <ul class="mb-0 pl-3 small">
            <% @pending_participant.missing_information_messages.each do |message| %>
              <li><%= message %></li>
            <% end %>
          </ul>
        </div>
      </div>
    <% end %>
  </div>
</div>

<div class="dashboard-card mt-3">
  <div class="dashboard-card__content p-3">
    <%= render "form", pending_participant: @pending_participant, photo_form: @photo_form %>
  </div>
</div>

<% if !@pending_participant.new_record? %>
  <div width="100%" class="row">
    <div class="col-md-8">
      <div id="documents-conditions-status-<%= @pending_participant.id %>" data-form-auto-submit-target="updateArea" class="dashboard-card dashboard-card__padding" style="margin-top: 20px;">
          <%= render "concerns/verifiables/conditions_status" %>
      </div>
    </div>
    <div class="col-md-4">
      <div id="documents-documents-status-<%= @pending_participant.id %>" data-form-auto-submit-target="updateArea" class="dashboard-card dashboard-card__padding" style="margin-top: 20px;">
          <%= render "concerns/verifiables/documents_status" %>
      </div>
    </div>
  </div>
  <div id="documents-upload-area-<%= @pending_participant.id %>" data-form-auto-submit-target="updateArea" class="dashboard-card dashboard-card__padding" style="margin-top: 20px;">
      <%= render "concerns/verifiables/document_upload_zones", site: @site, needed_document_types: @needed_document_types, documentable: @pending_participant %>
  </div>
<% end %>
