<% request_origin = local_assigns[:request_origin] || "pending_participant" %>
<% document = local_assigns[:document] || nil %>
<% from = determine_request_source(request.referer) %>
<% scope = determine_uploads_index_scope(request) %>
<% status = determine_uploads_index_status(request) %>

<%= form_with model: [@site, pending_participant],
              multipart: true,
              url: request_origin == "document" ? update_documentable_site_document_path(@site, document, from:, scope:, status:) : site_pending_participants_path(@site),
              method: request_origin == "document" ? :patch : :post,
              data: {
                controller: (pending_participant.new_record? ? "role-company" : "auto-submit role-company"),
                role_company_assignment_role_sites_api_url_value: by_assignment_api_participant_role_sites_path(site_id: @site.id),
                role_company_current_participant_role_site_id_value: @current_participant_role_site&.id,
                turbo: (request_origin == "document" ? true : (pending_participant.new_record? ? false : true)),
                role_company_same_company_roles_value: @site.participant_role_sites.select(&:same_company).map(&:id)
              } do |f| %>

  <!-- Disposition principale avec photo à gauche et formulaire à droite -->
  <div class="row">
    <% if photo_form %>
    <!-- Colonne de gauche pour la photo -->
    <div class="col-md-3">
      <!-- Conteneur d'image avec contrôleur Stimulus adapté selon le cas -->
      <%= render 'shared/profile_image',
                model: pending_participant,
                model_name: 'pending_participant',
                attribute_name: 'photo',
                preview_only: pending_participant.new_record?, # Preview only pour les nouveaux participants
                update_url: pending_participant.new_record? ? nil : update_image_site_pending_participant_path(@site, pending_participant),
                form: f %>
    </div>
    <% end %>
    <!-- Colonne de droite pour les informations -->
    <div class="<%= @photo_form ? "col-md-9" : "col-md-12" %>">
      <div width="100%" class="row mb-3">
        <div class="col-md-6">
          <div class="form-group">
            <%= f.label :first_name, "Prénom", class: "form-label" %>
            <%= f.text_field :first_name, class: "form-control",
                            data: pending_participant.new_record? ? {} : {
                              action: "input->auto-submit#submit"
                            } %>
            <%= render "shared/field_errors", object: pending_participant, field: :first_name %>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <%= f.label :last_name, "Nom", class: "form-label" %>
            <%= f.text_field :last_name, class: "form-control",
                            data: pending_participant.new_record? ? {} : {
                              action: "input->auto-submit#submit"
                            } %>
            <%= render "shared/field_errors", object: pending_participant, field: :last_name %>
          </div>
        </div>
      </div>

      <div class="row mb-3">
        <div class="col-md-6">
          <div class="form-group">
            <%= f.label :birth_date, "Date de naissance", class: "form-label" %>
            <%= f.text_field :birth_date, class: "form-control",
                            value: pending_participant.birth_date.present? ? pending_participant.birth_date.strftime("%d/%m/%Y") : nil,
                            data: { controller: "datepicker", action: "input->auto-submit#submit" }%>
            <%= render "shared/field_errors", object: pending_participant, field: :birth_date %>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <%= f.label :assignment_id, "Entreprise utilisatrice", class: "form-label" %>
            <%= f.select :assignment_id,
                options_for_select(
                  @assignments.map { |a| [a.company_name, a.id, { 'data-company-id': a.company_id }] },
                  pending_participant.assignment_id
                ),
                { prompt: "Sélectionnez une entreprise" },
                {
                  class: "form-control",
                  data: {
                    role_company_target: "assignmentSelect",
                    action: "change->role-company#assignmentChanged " + (pending_participant.new_record? ? "" : "change->auto-submit#submit")
                  }
                } %>
            <%= render "shared/field_errors", object: pending_participant, field: :assignment_id %>
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="form-group">
            <%= f.label :participant_role_site_id, "Rôle", class: "form-label" %>
            <%= f.collection_select :participant_role_site_id,
                [],
                :id,
                :name,
                { include_blank: "Sélectionnez un rôle" },
                class: "form-control",
                disabled: true,
                data: {
                  role_company_target: "roleSelect",
                  action: "change->role-company#roleChanged " + (pending_participant.new_record? ? "" : "change->auto-submit#submit")
                } %>
            <%= render "shared/field_errors", object: pending_participant, field: :participant_role_site_id %>
          </div>

        </div>
        <div class="col-md-6">
          <div class="form-group">
            <%= f.label :company_id, "Employeur", class: "form-label" %>
            <%= f.collection_select :company_id,
                @employers,
                :id,
                :name,
                { prompt: "Sélectionnez un employeur" },
                class: "form-control",
                data: {
                  role_company_target: "companySelect",
                  action: (pending_participant.new_record? ? "" : "change->auto-submit#submit")
                } %>
            <%= render "shared/field_errors", object: pending_participant, field: :company_id %>
          </div>
        </div>
      </div>
  <%= f.hidden_field :is_interim %>
      <% if pending_participant.new_record? %>
        <%= f.submit "Créer", class: "" %>
      <% end %>

    </div>
  </div>
<% end %>
