<div data-controller="attendee-form" class="container">
  <div class="page-banner mt-0 mb-3">
    <div class="page-banner__right" style="height: 3rem;">
      <div class="page-banner__action">
        <%= link_to site_site_meeting_path(site, site_meeting), data: { turbo_stream: true, turbo_action: "replace" }, class: "d-flex align-items-center flex-row" do %>
          <i class="fas fa-arrow-left action-icon" style="margin-right: 0.5rem;"></i>
          <span class="action-text">Retour</span>
        <% end %>
      </div>
    </div>
  </div>

  <%= simple_form_for [site, site_meeting], method: :post, url: site_site_meeting_attendees_path(site, site_meeting), data: { action: "submit->attendee-form#submitForm", turbo_stream: true } do |f| %>
    <div class="attendees-container">
      <% unless browser.device.tablet? || browser.device.mobile? %>
        <div class="attendees-header d-flex flex-wrap my-2 align-items-end">
          <div class="col-6 col-lg-2 text-center text-lg-start">Prénom</div>
          <div class="col-6 col-lg-2 text-center text-lg-start">Nom</div>
          <div class="col-12 col-lg-3 text-center text-lg-start">Email</div>
          <div class="col-12 col-lg-1 text-center">Envoyer QRCode</div>
          <div class="col-6 col-lg-3 text-center">Autorisation d'accès</div>
          <div class="col-6 col-lg-1 text-center">Toute la journée</div>
        </div>
      <% end %>

      <div class="attendees-list" data-attendee-form-target="attendeesList">
        <%= f.simple_fields_for :attendees do |attendee_form| %>
          <%= render 'attendee_fields', f: attendee_form %>
        <% end %>

        <% if site_meeting.attendees.size < 5 %>
          <% (5 - site_meeting.attendees.size).times do %>
            <%= f.simple_fields_for :attendees, Attendee.new, child_index: Time.now.to_i + rand(1000) do |attendee_form| %>
              <%= render 'attendee_fields', f: attendee_form %>
            <% end %>
          <% end %>
        <% else %>
          <%= f.simple_fields_for :attendees, Attendee.new, child_index: Time.now.to_i + rand(1000) do |attendee_form| %>
            <%= render 'attendee_fields', f: attendee_form %>
          <% end %>
        <% end %>
      </div>

      <div class="mt-3">
        <%= link_to "#", class: "mashe-button--primary rounded-4 p-2", data: { action: "click->attendee-form#addAttendee" } do %>
          <i class="fas fa-plus"></i> Ajouter un participant
        <% end %>
      </div>
    </div>

    <div class="mt-4">
      <%= f.button :submit, "Enregistrer", class: "btn btn-primary" %>
    </div>

    <template data-attendee-form-target="template">
      <%= f.simple_fields_for :attendees, Attendee.new, child_index: "NEW_RECORD" do |attendee_form| %>
        <%= render 'attendee_fields', f: attendee_form %>
      <% end %>
    </template>
  <% end %>
</div>
