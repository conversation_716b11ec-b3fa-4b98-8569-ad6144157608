<div class="page-banner mt-0 mb-3" >
 <div class="page-banner__right" style="height: 3rem;">
    <div class="page-banner__action"">
      <%= link_to site_site_meeting_path(@site, site_meeting), data: { turbo_stream: true, turbo_action: "replace" }, class: "d-flex align-items-center flex-row" do %>
        <i class="fas fa-arrow-left action-icon" style="margin-right: 0.5rem;"></i>
        <span class="action-text">Retour</span>
      <% end %>
    </div>
  </div>
</div>
<%= simple_form_for [site, site_meeting, attendee], data: { turbo_stream: true }, url: attendee.persisted? ? site_site_meeting_attendee_path(site, site_meeting, attendee) : site_site_meeting_attendees_path(site, site_meeting) do |f| %>
<div class="row align-items-center">
  <div class="col-5">
    <%= f.input :first_name, label: "Prénom", required: true, hint: "Exemple: John", error: "Le prénom est obligatoire" %>
  </div>
  <div class="col-5">
    <%= f.input :last_name, label: "Nom", hint: "Exemple: Doe", error: "Le nom est obligatoire" %>
  </div>
  <div class="col-2">
    <%= f.input :send_access_code, label: "Envoyer le QR Code", as: :boolean %>
  </div>
</div>
<div class="row align-items-center">
  <div class="col-12">
    <%= f.input :email, label: "Email", hint: "Exemple: <EMAIL>" %>
  </div>
</div>
  <hr>
  <p>Autorisation d'accès</p>
<div class="row attendee-row align-items-end" data-controller="attendee-form">
  <div class="col-10">
    <fieldset class="authorization-fields d-flex gap-3" <%= attendee.authorized_full_day? ? 'disabled' : '' %>>
      <div class="col-6">
        <%= f.input :authorized_from, as: :string, label: "Heure de début",
                    input_html: {
                      value: f.object.authorized_from&.strftime("%H:%M"),
                      data: { controller: "flatpickr", flatpickr_time_only_value: true }
                    } %>
      </div>
      <div class="col-6">
        <%= f.input :authorized_to, as: :string, label: "Heure de fin",
                    input_html: {
                      value: f.object.authorized_to&.strftime("%H:%M"),
                      data: { controller: "flatpickr", flatpickr_time_only_value: true }
                    } %>
      </div>
    </fieldset>
  </div>
  <div class="col-2">
    <%= f.input :full_day, label: "Jour entier", as: :boolean,
                input_html: {
                  class: "form-check-input",
                  data: { action: "change->attendee-form#toggleAuthorization" },
                  checked: attendee.authorized_full_day?
                } %>
  </div>
</div>


  <%= f.submit attendee.persisted? ? "Mettre à jour" : "Créer" do %>
    <i class="fas fa-<%= attendee.persisted? ? 'save' : 'plus' %> action-icon"></i>
    <span class="action-text"><%= attendee.persisted? ? "Mettre à jour" : "Créer" %></span>
  <% end %>
<% end %>
