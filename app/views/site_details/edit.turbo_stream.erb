<%= turbo_stream.update "modal-container" do %>
  <div class="mashe-modal" data-controller="mashe-modal">
    <div class="mashe-modal__overlay" data-action="click->mashe-modal#close"></div>
    <div class="mashe-modal__content">
      <button class="mashe-modal__close" data-action="click->mashe-modal#close">&times;</button>
      <div class="mashe-modal__body">
        <%= form_with model: @site_detail, url: site_site_detail_path(@site), method: :patch, data: { turbo: true } do |f| %>
          <%= f.hidden_field :redirect_to, value: params[:redirect_to] %>
          <div class="modal-header">
            <h5 class="modal-title">Modifier les informations du site</h5>
          </div>

          <div class="modal-body">
            <% if defined?(@errors) && @errors.any? %>
              <div class="alert alert-danger">
                <ul class="mb-0">
                  <% @errors.each do |error| %>
                    <li><%= error %></li>
                  <% end %>
                </ul>
              </div>
            <% end %>

            <h6 class="mb-3">Informations générales</h6>
            
            <div class="row mb-3">
              <div class="col-md-6">
                <%= f.label :name, "Nom du site", class: "form-label" %>
                <%= f.text_field :name, value: @site.name, class: "form-control", required: true %>
              </div>
              <div class="col-md-6">
                <%= f.label :short_name, "Nom court", class: "form-label" %>
                <%= f.text_field :short_name, value: @site.short_name, class: "form-control", required: true %>
              </div>
            </div>

            <div class="form-group mb-3">
              <%= f.label :address, "Adresse", class: "form-label" %>
              <%= f.text_field :address, value: @site.address, class: "form-control", required: true %>
            </div>

            <div class="form-group mb-3">
              <%= f.label :description, "Description", class: "form-label" %>
              <%= f.text_area :description, class: "form-control", rows: 3 %>
            </div>

            <div class="form-group mb-3 d-flex align-items-center">
              <%= f.label :base_email, "Email de base", class: "form-label me-3 mb-0" %>
              <div class="input-group w-auto flex-grow-1">
                <%= f.text_field :base_email, value: @site_detail.base_email, class: "form-control", placeholder: "contact", autocomplete: "off", pattern: "[^@\\s]+", style: "max-width: 200px;" %>
                <span class="input-group-text">@mashe.dev</span>
              </div>
            </div>
            <small class="form-text text-muted ms-2">Email principal pour les communications du site</small>

            <hr class="my-4">
            <h6 class="mb-3">Détails techniques</h6>

            <div class="row mb-3">
              <div class="col-md-6">
                <%= f.label :floor_area, "Surface (m²)", class: "form-label" %>
                <%= f.number_field :floor_area, class: "form-control", min: 0 %>
              </div>
              <div class="col-md-6">
                <%= f.label :permit_number, "Numéro de permis", class: "form-label" %>
                <%= f.text_field :permit_number, class: "form-control" %>
              </div>
            </div>

            <div class="row mb-3">
              <div class="col-md-6">
                <%= f.label :permit_date, "Date du permis", class: "form-label" %>
                <%= f.date_field :permit_date, class: "form-control" %>
              </div>
              <div class="col-md-6">
                <%= f.label :site_image, "Image du site", class: "form-label" %>
                <%= f.file_field :site_image, class: "form-control", accept: "image/*" %>
                <% if @site_detail.site_image.attached? %>
                  <small class="form-text text-muted">Image actuelle: <%= @site_detail.site_image.filename %></small>
                <% end %>
              </div>
            </div>
          </div>

          <div class="modal-footer">
            <%= f.submit "Enregistrer", class: "mashe-button mashe-button--primary" %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
<% end %> 