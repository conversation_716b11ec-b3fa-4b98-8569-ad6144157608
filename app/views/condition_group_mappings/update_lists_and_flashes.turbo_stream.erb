<%# app/views/condition_group_mappings/update_lists_and_flashes.turbo_stream.erb %>

<%# Remplacer tout le contenu du gestionnaire pour ce groupe spécifique %>
<%= turbo_stream.replace "condition-group-manager-#{@condition_group.id}" do %>
  <%= render partial: "condition_group_mappings/form",
             locals: {
               site: @site,
               condition_group: @condition_group,
               available_conditions: @available_conditions,
               available_logical_nodes: @available_logical_nodes,
               elements_in_group: @elements_in_group
             } %>
<% end %>

<%# S'assurer que les messages flash sont affichés (vous devez avoir un partial `shared/flashes` et un target `flashes` dans votre layout) %>
<%= turbo_stream.prepend "flashes" do %>
  <%= render partial: "shared/flashes" %>
<% end %>
