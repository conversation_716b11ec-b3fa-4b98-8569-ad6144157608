<div class="condition-group-manager" id="condition-group-manager-<%= @condition_group.id %>">
  <div class="row">
    <%# Colonne pour ajouter des éléments %>
    <div class="col-md-6">
      <div class="card shadow-sm mb-3 mb-md-0"> <%# mb-md-0 pour supprimer la marge en bas sur les écrans moyens et plus %>
        <div class="card-header bg-light border-bottom">
          <h5 class="mb-0 fs-6 fw-normal">Ajouter un élément au groupe</h5>
        </div>
        <div class="card-body p-3">
          <%# Section pour ajouter une Condition %>
          <div class="mb-3"> <%# Espacement entre les sections d'ajout %>
            <label class="form-label small text-muted">Conditions disponibles</label>
            <% if @available_conditions.present? %>
              <%= form_with(url: site_condition_group_condition_group_mappings_path(@site, @condition_group), method: :post, data: { turbo_frame: "_top" }) do |form| %>
                <%= form.hidden_field :element_type, value: 'Condition' %>
                <div class="input-group">
                  <%= form.select :element_id,
                                  options_from_collection_for_select(@available_conditions, :id, :name),
                                  { include_blank: "Sélectionner une condition..." },
                                  { class: "form-select form-select-sm" } %>
                  <%= form.button type: :submit, class: "btn btn-sm btn-outline-primary" do %>
                    <i class="fas fa-plus me-1"></i> Ajouter
                  <% end %>
                </div>
              <% end %>
            <% else %>
              <p class="text-muted small mb-0 fst-italic">Aucune autre condition disponible.</p>
            <% end %>
          </div>

          <hr class="my-3"> <%# Séparateur visuel plus subtil %>

          <%# Section pour ajouter un Nœud Logique %>
          <div>
            <label class="form-label small text-muted">Nœuds logiques disponibles</label>
            <% if @available_logical_nodes.present? %>
              <%= form_with(url: site_condition_group_condition_group_mappings_path(@site, @condition_group), method: :post, data: { turbo_frame: "_top" }) do |form| %>
                <%= form.hidden_field :element_type, value: 'LogicalNode' %>
                <div class="input-group">
                  <%= form.select :element_id,
                                  options_from_collection_for_select(@available_logical_nodes, :id, :name),
                                  { include_blank: "Sélectionner un nœud logique..." },
                                  { class: "form-select form-select-sm" } %>
                  <%= form.button type: :submit, class: "btn btn-sm btn-outline-primary" do %>
                    <i class="fas fa-plus me-1"></i> Ajouter
                  <% end %>
                </div>
              <% end %>
            <% else %>
              <p class="text-muted small mb-0 fst-italic">Aucun autre nœud logique disponible.</p>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <%# Colonne pour les éléments dans le groupe %>
    <div class="col-md-6">
      <div class="card shadow-sm">
        <div class="card-header bg-light border-bottom">
          <h5 class="mb-0 fs-6 fw-normal">Éléments dans: <span class="fw-semibold"><%= @condition_group.humanized_name %></span></h5>
        </div>
        <div class="card-body p-0"> <%# p-0 pour que la liste prenne toute la place si elle est le seul contenu %>
          <%= turbo_frame_tag "group_elements_#{@condition_group.id}" do %>
            <div class="list-group list-group-flush assigned-elements-list"> <%# list-group-flush pour enlever les bordures externes %>
              <% if @elements_in_group.present? %>
                <% @elements_in_group.each do |element| %>
                  <% mapping = @condition_group.condition_group_mappings.find_by(element: element) %>
                  <% if mapping %>
                    <div class="list-group-item d-flex justify-content-between align-items-center px-3 py-2">
                      <div>
                        <span class="badge bg-secondary-subtle text-secondary-emphasis rounded-pill me-2 small">
                          <%= element.class.model_name.human %>
                        </span>
                        <span class="small"><%= element.name %></span>
                      </div>
                      <%= link_to site_condition_group_condition_group_mapping_path(@site, @condition_group, mapping.id),
                          class: "btn btn-sm btn-outline-danger py-0 px-1",
                          data: { turbo_method: :delete, turbo_frame: "group_elements_#{@condition_group.id}" } do %>
                        <i class="fas fa-times"></i>
                      <% end %>
                    </div>
                  <% end %>
                <% end %>
              <% else %>
                <div class="list-group-item px-3 py-2">
                  <p class="text-muted small mb-0 fst-italic">Aucun élément dans ce groupe.</p>
                </div>
              <% end %>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>
