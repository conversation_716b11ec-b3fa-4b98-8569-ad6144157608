<% scope = local_assigns[:scope] || determine_uploads_index_scope(request) %>
<% status = local_assigns[:status] || determine_uploads_index_status(request) %>
<% from = local_assigns[:from] || determine_request_source(request.fullpath) || determine_request_source(request.referer) %>
<%
  dataset = DataTables::MasheDataTable.new()
  dataset.title = "Liste des documents"
  dataset.tab_title = "Documents"
  dataset.id = "uploads-index-datatable"
  dataset.rows = uploads
  dataset.searchable = true
  dataset.pagination = { initial_per_page: 10, per_page_options: [10, 25, 50] }
  dataset.checkable = {
    method: :id
  }
  dataset.columns = [
    {
      header: "Statut",
      method: :status_verbose,
      formatter: :indicator,
      sortable: true,
      classes: ["col-lg-1"]
    },
    {
      header: "Nom du fichier",
      method: :truncated_name,
      formatter: :turbo_link,
      format_params: [:site_upload_path, site, "self", { scope:, status: }],
      sortable: true,
      classes: ["col-lg-3", "justify-content-start", "hover-span-cell"]
    },
    {
      header: "Importé par",
      method: :user_full_name,
      sortable: true,
      classes: ["col-lg-3"]
    },
    {
      header: "Date d'import",
      method: :created_at,
      formatter: :date,
      sortable: true,
      classes: ["col-lg-2"]
    },
    {
      header: "Méthode d'import",
      method: :import_method_verbose,
      sortable: true,
      classes: ["col-lg-1"]
    }
  ]
  dataset.bulk_actions = [
    { name: "Supprimer", target_url: site_uploads_path(site, scope:, status:, from:), action_type: "turbo", turbo_method: "delete", turbo_confirm: "Êtes-vous sûr de vouloir supprimer l'importation ? Tous les documents associés seront supprimés.", permissions_target: @current_user_site, permissions: [:manage_documents] },
    {
      name: "Marquer comme Validé",
      target_url: bulk_validate_site_uploads_path(site), # Nouvelle route helper
      action_type: "turbo",
      turbo_method: "patch",
      turbo_confirm: "Êtes-vous sûr de vouloir marquer les documents comme validés ?",
      permissions_target: @current_user_site,
      permissions: [:validate_documents],
      conditions: [{
        object: request.fullpath,
        method: :include?,
        args: "validated",
        value: false
      }]
    }
  ]
  dataset.actions = [
    {
      link: {
        content: "Supprimer",
        path: :site_upload_path,
        args: [site, "self"],
        kwargs: {
          scope:,
          status:,
          from:
        },
        turbo_stream: true,
        turbo_method: :delete,
        turbo_confirm: "Êtes-vous sûr de vouloir supprimer l'importation ? Tous les documents associés seront supprimés."
      }
    }
  ]
  dataset.mobile_row_type = 'card'
  dataset.mobile_card_header = [
    { header: "Méthode d'import", method: :import_method_verbose },
    { header: "Statut", method: :status_verbose, formatter: :indicator }
  ]
  dataset.mobile_card_body = [
    { header: "Nom du fichier", method: :filename },
    { header: "Date d'import", method: :created_at, formatter: :date },
    { header: "Importé par", method: :user_full_name }
  ]
  dataset.build_hash
%>

<%= render partial: "shared/data_table/data_table_wrapper", locals: { dataset: dataset } %>
