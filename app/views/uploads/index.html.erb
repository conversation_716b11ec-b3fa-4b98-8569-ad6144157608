<div class="page-banner">
  <div class="page-banner__left">
    <% if permission?(:add_documents) %>
      <div class="page-banner__action">
        <%= link_to "#", data: { action: "click->import-sidebar#toggle:prevent" } do %>
          <i class="fas fa-file-upload action-icon"></i>
          <span class="action-text">Importer</span>
        <% end %>
      </div>
    <% end %>

    <% if @topbar_tab == "archived" %>
      <%= render partial: "uploads/completed_uploads_count", locals: { count: @uploads.count } %>
    <% else %>


      <%= render partial: "uploads/pending_uploads_count", locals: { count: @uploads.where(status: :pending).count } %>


      <%= render partial: "uploads/error_uploads_count", locals: { count: @uploads.where(status: :error).count } %>


    <% end %>
  </div>
</div>

<div id="upload-scope-filter" role="group" class="<%= browser.device.mobile? ? "mobile" : "" %>">
  <% if permission?(:view_own_uploads) %>
    <%= link_to site_uploads_path(@site, scope: "user", status: @topbar_tab == "uploads" ? nil : @topbar_tab),
        class: "filter-btn #{params[:scope].nil? || params[:scope] == 'user' ? 'active' : ''}" do %>
      <i class="fas fa-user me-2"></i>Mes documents
    <% end %>
  <% end %>

  <% if permission?(:view_uploads_with_subcontractors) %>
    <%= link_to site_uploads_path(@site, scope: "company", status: @topbar_tab == "uploads" ? nil : @topbar_tab),
        class: "filter-btn #{params[:scope] == 'company' ? 'active' : ''}" do %>
      <i class="fas fa-building me-2"></i>Documents <%= current_user.company.name %>
    <% end %>
  <% end %>

  <% if permission?(:view_all_uploads) %>
    <%= link_to site_uploads_path(@site, scope: "all", status: @topbar_tab == "uploads" ? nil : @topbar_tab),
        class: "filter-btn #{params[:scope] == 'all' ? 'active' : ''}" do %>
      <i class="fas fa-list me-2"></i>Tous
    <% end %>
  <% end %>
</div>

<div class="dashboard-card" style="margin-top: 20px;" id="uploads-index-datatable-container">
  <%= render partial: "uploads/index", locals: { uploads: @uploads, site: @site } %>
</div>
