<% scope = local_assigns[:scope] || determine_uploads_index_scope(request) %>
<% status = local_assigns[:status] || determine_uploads_index_status(request) %>
<% from = local_assigns[:from] || determine_request_source(request.referer) %>

<div class="mashe-sidebar__title">Import du <%= l upload.created_at, format: :long %></div>
<div class="mashe-sidebar__subtitle">par <%= upload.user_full_name %></div>

<div class="ms-3">
  <%= link_to "Contrôler les documents", site_upload_path(site, id: upload.id), class: "mashe-link mashe-link__italic" %>
</div>
<div class="documents-items">
  <% upload.documents.each do |document| %>
    <%= render partial: "documents/document_item", locals: { document:, scope:, status: }, formats: [:html] %>
  <% end %>
</div>

<div class="d-flex justify-content-end mx-4">
  <%= form_with(model: [site, upload], local: true) do |f| %>
    <%= f.hidden_field :status, value: :validated %>
    <%= f.submit "Noté comme vérifié", class: "mashe-button--primary " %>
  <% end %>
  <%= link_to "Supprimer l'importation", site_upload_path(site, id: upload.id, scope:, status:, from:), data: { turbo_method: :delete, turbo_confirm: "Êtes-vous sûr de vouloir supprimer l'importation ? Tous les documents associés seront supprimés." }, class: "delete-document-button" %>
</div>
