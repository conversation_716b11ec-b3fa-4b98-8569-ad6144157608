<div class="page-banner">
  <div class="page-banner__left">
    <div class="stat-item dashboard-card">
      <span class="stat-number"><%= @upload.filename %></span>
      <span class="stat-label">Nom du fichier</span>
    </div>

    <div class="stat-item dashboard-card">
      <span class="stat-number"><%= @upload.user.full_name %></span>
      <span class="stat-label">Importé par</span>
    </div>

    <div class="stat-item dashboard-card">
      <span class="stat-number"><%= l(@upload.created_at, format: :short) %></span>
      <span class="stat-label">Date d'import</span>
    </div>

    <div class="stat-item dashboard-card">
      <span class="stat-number"><%= @upload.status_verbose %></span>
      <span class="stat-label">Statut</span>
    </div>
  </div>
</div>

<% @documents.each do |document| %>
  <div class="dashboard-card" style="margin-top: 20px; padding: 20px;" id="upload-show-document-<%= document.id %>">
    <div class="document-edit-container" style="width: 100%;">
      <div class="row">
        <!-- PDF à gauche -->
        <div class="col-md-7">
          <div class="document-preview-container mb-4" style="height: 100%; max-height: 800px;">
            <%= render partial: "documents/preview", locals: { document: document }, formats: [:html] %>
          </div>
        </div>

        <!-- Formulaire d'édition à droite -->
        <div class="col-md-5">
          <div id="document-edit-container">
            <%= render partial: "documents/extracted_fields", locals: { document: document, from: "uploads_show" }, formats: [:html] %>
          </div>
        </div>
      </div>
    </div>
  </div>
<% end %>

<div class="d-flex justify-content-end mt-4">
  <%= form_with(model: [@site, @upload], local: true) do |f| %>
    <%= f.hidden_field :status, value: :validated %>
    <%= f.submit "Confirmer la vérification", class: "btn btn-primary" %>
  <% end %>
</div>
