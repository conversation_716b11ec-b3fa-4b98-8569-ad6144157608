<div class="documents-status" id="documents_status">
  <div class="dashboard-card__title-container">
    <h5 class="dashboard-card__title">Documents</h5>
  </div>
  <% documents = local_assigns[:validation_status]&.dig(:documents) || @validation_status[:documents] %>

  <% if documents.any? %>
    <% documents.each do |doc_status| %>
      <div class="mb-3 position-relative document-line"
           data-controller="tooltip"
           data-action="mouseenter->tooltip#show mouseleave->tooltip#hide">
        <div class="d-flex align-items-center justify-content-between">
          <div class="d-flex align-items-center">

            <% if doc_status&.key?(:status) %>
              <span class="indicator indicator--<%= doc_status[:status] %> me-2">
                <% case doc_status[:status] %>
                <% when :success %>
                  ✓
                <% when :danger %>
                  ✕
                <% when :neutral %>
                  -
                <% end %>
              </span>
            <% end %>

            <% if doc_status[:failed_fields]&.any? && doc_status[:document_type].present? %>
              <div class="document-info-container d-flex align-items-center">
                <i class="fas fa-info-circle text-warning me-2"></i>
              </div>
            <% end %>
            <% if doc_status[:document_type].present? %>
              <span><%= doc_status[:document_type]&.humanized_name %></span>
            <% else %>
              <span class="text-muted">Non renseigné</span>
            <% end %>
          </div>

          <div class="document-actions d-flex gap-2">
            <%= link_to edit_document_path(doc_status[:document]),
                      class: "indicator indicator--info indicator--filled indicator--lg",
                      data: {turbo_stream: true},
                      title: "Éditer ce document" do %>
              <i class="fas fa-eye"></i>
            <% end %>
            <% partials_config = [
                 {
                   id: "documents_status",
                   partial: "concerns/verifiables/documents_status"
                 },
                 {
                   id: "conditions_status",
                   partial: "concerns/verifiables/conditions_status"
                 }
               ] %>
            <%= link_to document_path(doc_status[:document].id, from: local_assigns[:from] || determine_request_source(request.fullpath)),
                class: "indicator indicator--danger indicator--filled indicator--lg",
                data: {
                  turbo_method: :delete,
                  turbo_confirm: "Êtes-vous sûr de vouloir supprimer ce document ? Cette action est irréversible et peut affecter la validation de l'intervenant."
                },
                title: "Supprimer ce document" do %>
                <p style="transform: rotate(45deg); font-size: 1rem;">+</p>
            <% end %>
          </div>
        </div>

        <% if doc_status[:failed_fields]&.any? %>
          <div class="failed-fields position-absolute bg-white shadow p-2 rounded d-none"
               data-tooltip-target="content"
               style="right: 0; z-index: 10000; min-width: 300px; border: 1px solid #dee2e6;">
            <div class="errors-title border-bottom pb-1 mb-2 d-flex justify-content-between">
              <strong class="text-danger">Erreurs détectées</strong>
              <i class="fas fa-exclamation-triangle text-warning"></i>
            </div>
            <% doc_status[:failed_fields].each do |field_status| %>
              <div class="failed-field mb-1">
                <small class="text-danger d-block">
                  <%= field_status[:message] %>
                  <% if field_status[:field] %>
                    <span class="text-muted">
                      (Champ : <%= field_status[:field]&.humanized_name %> incomplet ou non satisfaisant)
                    </span>
                  <% end %>
                </small>
              </div>
            <% end %>
          </div>
        <% end %>
      </div>
    <% end %>
  <% else %>
    <p class="mb-3">Aucun document n'a été lié à cet intervenant pour le moment.</p>
  <% end %>
</div>
