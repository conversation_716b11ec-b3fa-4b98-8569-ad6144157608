<div id="stakeholders-section" class="dashboard-card stakeholders-section" style="margin-top: 20px;">
  <h2 class="data-table-title">Parties prenantes</h2>

  <div class="stakeholders-scroll">
    <% stakeholders.each do |stakeholder| %>
      <div class="stakeholder-card">
        <%= link_to edit_site_stakeholder_path(site, stakeholder),
                    class: 'edit-button',
                    data: { turbo_stream: true } do %>
          <i class="fas fa-edit edit-button-icon"></i>
        <% end %>
        <div class="stakeholder-content">
          <div class="stakeholder-logo-wrapper">
            <% if stakeholder.logo.attached? %>
              <div class="stakeholder-logo">
                <%= image_tag stakeholder.logo %>
              </div>
            <% else %>
              <div class="empty-logo">
                <i class="fas fa-building"></i>
              </div>
            <% end %>
          </div>

          <div class="stakeholder-info">
            <h3 class="stakeholder-name text-truncate"><%= stakeholder.name %></h3>
            <p class="stakeholder-role text-truncate"><%= stakeholder.role %></p>

            <div class="stakeholder-details">
              <% if stakeholder.address.present? %>
                <div class="detail-item">
                  <i class="fas fa-map-marker-alt"></i>
                  <span class="text-truncate"><%= stakeholder.address %></span>
                </div>
              <% end %>

              <% stakeholder.other_infos.each do |key, value| %>
                <% if value.present? %>
                  <div class="detail-item">
                    <span class="text-truncate"><%= key %>: <%= value %></span>
                  </div>
                <% end %>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>