<% scope = determine_uploads_index_scope(request) || params[:scope] %>
<% status = determine_uploads_index_status(request) || params[:status] %>

<div id="document-item-<%= document.id %>">
  <div class="document-item">

    <%= link_to edit_document_path(document, scope:, status:),
                class: "document-item__expand",
                data: { turbo_stream: true } do %>
      <i class="fas fa-expand-alt"></i>
    <% end %>
    <%= link_to document_path(document, scope:, status:, from: determine_request_source(request.referer)),
                class: "document-item__delete",
                data: { turbo_stream: true, turbo_method: :delete, turbo_confirm: "Êtes-vous sûr de vouloir supprimer ce document ?" } do %>
      <i class="fas fa-times"></i>
    <% end %>

    <div class="document-item__visual"
        data-controller="image-preview"
        data-image-preview-src-value="<%= serve_document_path(document) %>"
        data-image-preview-content-type-value="pdf"
        data-image-preview-max-width-value="600"
        data-image-preview-max-height-value="800"
        data-image-preview-thumbnail-width-value="80"
        data-image-preview-thumbnail-height-value="100">
      <img class="document-thumbnail" data-image-preview-target="thumbnail">
    </div>

    <div class="document-item__content">
      <div class="document-item__type">
        <%= document.document_type&.humanized_name || "Type non identifié" %>
      </div>

      <div class="document-item__documentable">
        <% if document.documentable %>
          <%= documentable_name(document.documentable) %>
        <% else %>
          Non associé
        <% end %>
      </div>

      <div class="document-item__status">
        <span class="indicator indicator--<%= document.status == "active" ? "success" : "neutral" %>">
          <%= t("documents.status.#{document.status}") %>
        </span>
      </div>
    </div>
  </div>
</div>
