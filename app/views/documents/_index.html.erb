
<%
  dataset = DataTables::MasheDataTable.new()
  dataset.title = "Liste des documents"
  dataset.rows = documents
  dataset.searchable = true
  dataset.pagination = { initial_per_page: 10, per_page_options: [10, 25, 50] }
  dataset.checkable = {
    method: :id
  }
  dataset.bulk_actions = [
    {
      name: "Archiver",
      target_url: archive_api_documents_path,
      action_type: "method",
      method: "patch",
      permissions_target: @current_user_site,
      permissions: [:manage_documents],
    }
  ]

  dataset.columns = [
    {
      header: "Statut",
      method: :status,
      formatter: :helper,
      format_params: [:status_verbose, validation_status],
      sortable: true,
      classes: ["col-1", "col-lg-1", "justify-content-start"]
    },
    {
      header: "Nom du fichier",
      method: :filename,
      sortable: true,
      classes: ["col-3", "col-lg-3", "justify-content-start", "hover-span-cell"],
      formatter: :turbo_link,
      format_params: [:edit_document_path, "self", "model" => params[:model], "model_id" => params[:id]]
    },
    {
      header: "Type de document",
      method: :document_type,
      formatter: :helper,
      format_params: [:document_type],
      sortable: true,
      classes: ["col-2", "col-lg-2", "justify-content-start"]
    },
    {
      header: "Importé par",
      method: :upload,
      formatter: :helper,
      format_params: [:user_full_name],
      sortable: true,
      classes: ["col-2", "col-lg-2", "justify-content-start"]
    },
    {
      header: "Date d'import",
      method: :upload,
      formatter: :helper,
      format_params: [:import_date],
      sortable: true,
      classes: ["col-1", "col-lg-1", "justify-content-start"]
    },
    {
      header: "Méthode d'import",
      method: :upload,
      formatter: :helper,
      format_params: [:import_method_verbose],
      sortable: true,
      classes: ["col-1", "col-lg-1", "justify-content-start", "text-start"]
    }

  ]
  # dataset.row_link = {
  #   path: :site_upload_path,
  #   other_params_path: [@site, "self"]
  # }
  dataset.actions = [
    {
      link: {
        content: "Voir",
        path: :edit_document_path,
        args: ["self"],
        kwargs: {
          model: params[:model],
          model_id: params[:id]
        },
        turbo_method: :get,
        turbo_stream: true
      }, conditions: [
        {
          object: browser.device,
          method: :mobile?,
          value: false
        }
      ]
    },
    {
      link: {
        content: "Voir l'historique",
        path: :document_history_document_path,
        args: ["self"],
        turbo_method: :get,
        turbo_stream: true
      }
    },
    {
      link: {
        content: "Supprimer",
        path: :document_path,
        args: ["self"],
        kwargs: {
          model: params[:model],
          model_id: params[:id],
          from: determine_request_source(request.fullpath)
        },
        turbo_method: :delete,
        turbo_confirm: "Êtes-vous sûr de vouloir supprimer ce document ?"
      }
    }
  ]
  dataset.mobile_row_type = 'card'
  dataset.mobile_card_header = [
    { header: "Méthode d'import", method: :upload, formatter: :helper, format_params: [:import_method_verbose] },
    { header: "Statut", method: :status, formatter: :helper, format_params: [:status_verbose, validation_status], }
  ]
  dataset.mobile_card_body = [
    { header: "Nom du fichier", method: :filename, formatter: :turbo_link, format_params: [:edit_document_path, "self"] },
    { header: "Type de document", method: :document_type, formatter: :helper, format_params: [:document_type] },
    { header: "Date d'import", method: :created_at, formatter: :date },
    { header: "Importé par", method: :upload, formatter: :helper, format_params: [:user_full_name] },
  ]

  dataset.build_hash
%>

<%= render partial: "shared/data_table/data_table_wrapper", locals: { dataset: dataset } %>
