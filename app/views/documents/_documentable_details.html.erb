<div class="documentable-card <%= 'documentable-card--empty' unless document.documentable.present? %>"
      data-controller="search-expandable">
  <% if document.documentable.present? %>

      <%= documentable_visual(document.documentable) %>


    <div class="documentable-card__content" id="documentable-card__content_participant_<%= document.id %>">
      <div class="documentable-card__name">
        <%= documentable_name(document.documentable) %>
      </div>

      <div class="documentable-card__metadata">
        <% documentable_metadata(document.documentable).each do |key, value| %>
          <% if value.present? %>
            <div class="documentable-card__metadata-item">
              <% case key %>
              <% when :birth_date %>
                <i class="fas fa-calendar-alt" style="color: #0d0a67"></i> <%= value %>
              <% when :employer %>
                <i class="fas fa-briefcase" style="color: #0d0a67"></i> <%= value %>
              <% when :role %>
                <i class="fas fa-id-badge" style="color: #0d0a67"></i> <%= value %>
              <% when :status %>
                <i class="fas fa-info-circle" style="color: #0d0a67"></i> <%= value %>
              <% when :company_number %>
                <i class="fas fa-hashtag" style="color: #0d0a67"></i> <%= value %>
              <% when :pending %>
                <i class="fas fa-clock" style="color: #0d0a67"></i> En attente validation
              <% else %>
                <i class="fas fa-circle" style="color: #0d0a67"></i> <%= value %>
              <% end %>
            </div>
          <% end %>
        <% end %>
      </div>

      <div class="documentable-card__validity-status">
        <%= group_results_indicators_for(document.documentable.on_site(@site)) %>
      </div>
      <div class="documentable-search">
        <div class="documentable-search__button"
              data-search-expandable-target="button"
              data-action="click->search-expandable#toggle">
          <i class="fas fa-search documentable-search__icon" data-search-expandable-target="icon"></i>
        </div>
        <div class="documentable-search__form" data-search-expandable-target="form">
          <% if document.document_type.present? %>
            <%= render "documents/documentable_form", document: document %>
          <% end %>
        </div>
        <% if document.personal_document? %>

          <%= link_to new_participant_site_documentable_path(@site, document_id: document.id), class: "documentable-search__button", data: { turbo_stream: true } do %>
            <i class="fas fa-plus documentable-search__icon"></i>
          <% end %>
        <% end %>
      </div>
    </div>

  <% else %>
    <div class="documentable-card__content" id="documentable-card__content_participant_<%= document.id %>">
      <div class="documentable-card__visual">
        <i class="fas fa-unlink fa-3x text-muted"></i>
      </div>
      <div class="documentable-card__name">Aucune association</div>
      <div class="documentable-card__metadata">
        <div class="documentable-card__metadata-item">
          <i class="fas fa-info-circle"></i> Associez ce document à une entité
        </div>
      </div>
      <div class="documentable-search">
        <div class="documentable-search__button"
              data-search-expandable-target="button"
              data-action="click->search-expandable#toggle">
          <i class="fas fa-search documentable-search__icon" data-search-expandable-target="icon"></i>
        </div>
        <div class="documentable-search__form" data-search-expandable-target="form">
          <% if document.document_type.present? %>
            <%= render "documents/documentable_form", document: document %>
          <% end %>
        </div>
        <% if document.personal_document? %>
          <%= link_to new_participant_site_documentable_path(@site, document_id: document.id), class: "documentable-search__button", data: { turbo_stream: true } do %>
            <i class="fas fa-plus documentable-search__icon"></i>
          <% end %>
        <% end %>
      </div>
    </div>
  <% end %>
</div>
<div class="documentable-search__overlay" data-search-expandable-target="overlay"></div>
