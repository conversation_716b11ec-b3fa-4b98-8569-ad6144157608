<div class="participant-creation-form pb-3" data-controller="participant-form role-company" data-participant-form-document-id-value="<%= document.id %>" data-role-company-same-company-roles-value="<%= @site.participant_role_sites.select(&:same_company).map(&:id).to_json %>" data-role-company-assignment-role-sites-api-url-value="<%= by_assignment_api_participant_role_sites_path(site_id: @site.id)%>">
  <div class="d-flex justify-content-end align-items-center mb-3">
    <button type="button" class="documentable-search__close-button" data-action="participant-form#cancel">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <div class="participant-form-container px-3">
    <div class="row mb-2">
      <div class="col-md-6">
        <div class="form-group">
          <input type="text" name="participant[person_attributes][first_name]" class="form-control form-control-sm" required placeholder="Prénom...">
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-group">
          <input type="text" name="participant[person_attributes][last_name]" class="form-control form-control-sm" required placeholder="Nom...">
        </div>
      </div>
    </div>

    <div class="row mb-2">
      <div class="col-md-6">
        <div class="form-group">
          <input type="text" name="participant[person_attributes][birth_date]" class="form-control form-control-sm" data-controller="datepicker" required placeholder="Date de naissance...">
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-group">
          <select name="participant[assignment_id]" class="form-control form-control-sm" data-role-company-target="assignmentSelect"  data-action="change->role-company#assignmentChanged" required>
            <option value="">Entreprise intervenante...</option>
            <% (@assignments || @site.assignments.includes(:company)).each do |assignment| %>
              <option value="<%= assignment.id %>" data-company-id="<%= assignment.company_id %>">
                <%= assignment.company_name %>
              </option>
            <% end %>
          </select>
        </div>
      </div>
    </div>

    <div class="row mb-2">
      <div class="col-md-6">
        <div class="form-group">
          <select name="participant[participant_role_site_id]" class="form-control form-control-sm" data-role-company-target="roleSelect" data-action="change->role-company#roleChanged" required disabled>
            <option value="">Selectionnez un rôle</option>
          </select>
        </div>
      </div>

      <div class="col-md-6">
        <div class="form-group">
          <select name="participant[person_attributes][company_id]" class="form-control form-control-sm" data-role-company-target="companySelect" required>
            <% (@employers || Company.all).each do |company| %>
              <option value="<%= company.id %>"><%= company.name %></option>
            <% end %>
          </select>
        </div>
      </div>
    </div>

    <div class="d-flex justify-content-between mt-3">
      <button type="button" class="mashe-button mashe-button--primary" data-action="participant-form#submitForm">
        <i class="fa-solid fa-check"></i>
      </button>
    </div>
  </div>
</div>
