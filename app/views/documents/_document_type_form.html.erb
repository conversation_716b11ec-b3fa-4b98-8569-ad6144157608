<% from = local_assigns[:from] || determine_request_source(request.referer) %>
<% scope = local_assigns[:scope] || determine_uploads_index_scope(request) %>
<% status = local_assigns[:status] || determine_uploads_index_status(request) %>

<%= simple_form_for document, url: update_document_type_site_document_path(@site, document, scope:, status:, from:), method: :patch, data: { controller: "auto-submit" } do |f| %>
  <% document_types = DocumentType.for_site(@site).where(target_type: document.document_type&.personal_document? ? [:person, :participant] : [:company, :assignment]).order(:name).map { |dt| [dt.humanized_name, dt.id] } %>
  <%= f.association :document_type,
                  collection: document_types,
                  label: false,
                  selected: document.document_type_id,
                  input_html: {
                    novalidate: true,
                    class: "form-select tomselect-field",
                    data: {
                      site_id: @site&.id,
                      document_id: document.id,
                      action: "change->auto-submit#submit"
                    }
                  } %>
<% end %>
