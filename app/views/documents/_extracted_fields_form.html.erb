<% if document.document_type.present? %>
  <div class="extracted-fields-list mt-3">
    <% if current_user.admin? %>
      <% document.document_type.document_fields.each do |field| %>
        <% is_full_width = %i[list].include?(field.format.to_sym) %>
        <div class="extracted-field <%= 'full-width' if is_full_width %>">
          <% extracted_field = document.extracted_fields.find_or_initialize_by(document_field: field) %>
          <%= simple_fields_for "extracted_fields[#{field.id}]", extracted_field do |ef| %>
            <%= render_document_field_input(ef, field, extracted_field) %>
            <%= ef.input :document_field_id, as: :hidden, input_html: { value: field.id } %>
          <% end %>
        </div>
      <% end %>
    <% else %>
      <% document.document_type.document_fields.filter(&:displayed).each do |field| %>
        <% is_full_width = %i[list].include?(field.format.to_sym) %>
        <div class="extracted-field <%= 'full-width' if is_full_width %>">
          <% extracted_field = document.extracted_fields.find_or_initialize_by(document_field: field) %>
          <%= simple_fields_for "extracted_fields[#{field.id}]", extracted_field do |ef| %>
            <%= render_document_field_input(ef, field, extracted_field) %>
            <%= ef.input :document_field_id, as: :hidden, input_html: { value: field.id } %>
          <% end %>
        </div>
      <% end %>
    <% end %>
  </div>
<% end %>
