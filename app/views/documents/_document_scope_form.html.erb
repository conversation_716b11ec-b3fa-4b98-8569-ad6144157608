<% from ||= determine_request_source(request.referer) %>
<% scope_param ||= determine_uploads_index_scope(request) %> <%# Renommé pour éviter confusion avec le document_scope %>
<% status ||= determine_uploads_index_status(request) %>

<%= simple_form_for document, url: update_document_scope_site_document_path(site, document, scope: scope_param, status: status, from: from), method: :patch, html: { id: "document_scope_form_#{document.id}", class: "document-scope-form" }, data: { controller: "auto-submit" } do |f| %>
  <div class="document-scope mb-3">
    <div class="document-scope-selector">
      <div class="form-check form-check-inline">
        <%= f.radio_button :document_scope, 'personal', id: "scopePersonal_#{document.id}", class: "form-check-input", checked: document.document_type&.personal_document?, data: { action: "change->auto-submit#submit" } %>
        <%= f.label :document_scope_personal, "Document personnel", for: "scopePersonal_#{document.id}", class: "form-check-label" %>
      </div>
      <div class="form-check form-check-inline">
        <%= f.radio_button :document_scope, 'enterprise', id: "scopeEnterprise_#{document.id}", class: "form-check-input", checked: document.document_type&.enterprise_document?, data: { action: "change->auto-submit#submit" } %>
        <%= f.label :document_scope_enterprise, "Document d'entreprise", for: "scopeEnterprise_#{document.id}", class: "form-check-label" %>
      </div>
    </div>
  </div>
<% end %>
