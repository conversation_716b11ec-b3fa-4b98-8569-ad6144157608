<% from = determine_request_source(request.referer) %>
<div class="documentable-form" data-action="click@window->search-expandable#clickOutside">
  <%= form_with(model: document, url: update_documentable_site_document_path(@site, document, from:), method: :patch, data: { controller: "auto-submit" }) do |f| %>
    <div class="search-selector"
         data-controller="search-selector"
         data-search-selector-url-value="<%= api_documentable_searches_path(@site || document.site) %>"
         data-search-selector-site-id-value="<%= (@site || document.site).id %>"
         data-search-selector-partial-value="<%= "documentables/documentable_result" %>"
         data-search-selector-target-model-value="<%= document.document_type&.target_model.to_s %>"
         data-search-selector-min-length-value="2">

      <%= text_field_tag :search,
                        nil,
                        placeholder: "Saisissez votre recherche...",
                        class: "form-control mashe-search-input",
                        autocomplete: "off",
                        data: {
                          search_selector_target: "input",
                          search_expandable_target: "input",
                          action: "input->search-selector#search"
                        } %>

      <%= f.hidden_field :documentable_id,
                        data: {
                          search_selector_target: "hiddenInput",
                          action: "change->auto-submit#submit"
                        } %>
      <%= f.hidden_field :documentable_type,
                        value: document.document_type&.target_model.to_s %>

      <div class="search-results"
           data-search-selector-target="results"
           style="display: none;">
      </div>
    </div>
  <% end %>
</div>
