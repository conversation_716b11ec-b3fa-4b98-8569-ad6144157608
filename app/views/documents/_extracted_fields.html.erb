<% document_validation_status = local_assigns[:document_validation_status] || nil %>
<% scope = local_assigns[:scope] || determine_uploads_index_scope(request) %>
<% status = local_assigns[:status] || determine_uploads_index_status(request) %>
<% from = local_assigns[:from] || determine_request_source(request.referer) %>

<%= render partial: "documents/extracted_fields_errors", locals: { document: document, document_validation_status: document_validation_status } %>

<div class="extracted-fields document-edit-form" id="document-edit-form-<%= document.id %>">
  <%# Section pour le Type de Document %>
  <% if (document.document_type.nil? || document.document_type.document_category == "imported") && permission?(:manage_documents) %>
    <div class="px-3">
      <%= render partial: "documents/document_scope_form", locals: { document: document, site: @site, scope: scope, status: status } %>
      <%= render partial: "documents/document_type_form", locals: { document: document, site: @site, scope: scope, status: status } %>
    </div>
  <% else %>
    <h5 class="">
      Type : <%= document.document_type.humanized_name %>
    </h5>
    <%# Si le type n'est pas modifiable ici, on pourrait quand même avoir besoin de son ID pour d'autres opérations %>
    <%# Mais le formulaire principal pour les champs extraits n'en aura pas besoin directement s'il est séparé %>
  <% end %>

  <%# Section pour le Documentable %>
  <div id="documentable-details_<%= document.id %>" class="px-3">
    <%= render partial: "documents/documentable_details", locals: { document: document, site: @site }, formats: [:html] %>
  </div>

  <%# Section pour les Associations (si pertinent et non géré par documentable) %>
  <% if document.document_type %>
    <% represented_model = document.document_type.represented_model_name %>
    <% if represented_model.present? %>
      <div class="document-associations-container mb-3 px-3">
        <%= render "documents/document_associations_section",
                   document: document,
                   represented_model: represented_model %>
        <%# Ce partiel nécessitera peut-être son propre formulaire s'il y a des actions d'édition %>
      </div>
    <% end %>
  <% end %>

  <%# Formulaire pour les Champs Extraits %>
  <% if document.document_type.present? && document.document_type.document_fields.any? %>
    <div class="px-3">
      <%= simple_form_for document, url: update_extracted_fields_site_document_path(@site, document, scope:, status:, from:), data: { controller: "auto-submit" }, method: :patch do |f| %>
        <div id="extracted-fields-container-<%= document.id %>" class="extracted-fields-container">
          <%# _extracted_fields_form.html.erb ne contient que les champs, pas la balise form %>
          <%= render partial: "documents/extracted_fields_form", locals: { document: document } %>
        </div>
      <% end %>
    </div>
  <% elsif document.document_type.present? %>
    <div class="px-3">
      <p class="text-muted">Ce type de document ne contient aucun champ spécifique à remplir.</p>
    </div>
  <% end %>


  <div class="mt-3 d-flex justify-content-end">
    <%= link_to "Supprimer ce document", document_path(id: document.id, site_id: @site&.id, scope:, status:, from:), data: { turbo_method: :delete, turbo_confirm: "Êtes-vous sûr de vouloir supprimer ce document ?" }, class: "mashe-button--danger rounded-4 px-2 py-1 align-self-center" %>
  </div>
</div>
