<div class="condition-group-manager" data-controller="condition-group-manager">
  <div class="row">
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h5>Conditions disponibles</h5>
        </div>
        <div class="card-body">
          <div class="available-conditions">
            <% @available_conditions.each do |condition| %>
              <div class="condition-item" data-condition-id="<%= condition.id %>">
                <div class="condition-info">
                  <h6><%= condition.name %></h6>
                  <small><%= condition.condition_type %></small>
                </div>
                <%= button_to site_condition_group_condition_group_mappings_path(@site, @condition_group, condition_id: condition.id),
                    class: "btn btn-sm btn-outline-primary",
                    data: { turbo_method: :post } do %>
                  <i class="fas fa-plus"></i>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h5>Conditions dans le groupe</h5>
        </div>
        <div class="card-body">
          <div class="group-conditions">
            <%= turbo_frame_tag "group_conditions" do %>
              <% @condition_group.conditions.each do |condition| %>
                <div class="condition-item" data-condition-id="<%= condition.id %>">
                  <div class="condition-info">
                    <h6><%= condition.name %></h6>
                    <small><%= condition.condition_type %></small>
                  </div>
                  <%= button_to site_condition_group_condition_group_mapping_path(@site, @condition_group, condition_id: condition.id),
                      class: "btn btn-sm btn-outline-danger",
                      data: { turbo_method: :delete } do %>
                    <i class="fas fa-times"></i>
                  <% end %>
                </div>
              <% end %>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
