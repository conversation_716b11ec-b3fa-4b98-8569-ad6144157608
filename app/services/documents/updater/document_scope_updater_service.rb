module Documents
  module Updater
    class DocumentScopeUpdaterService
      attr_reader :global_renderer

      def initialize(user:, request:)
        @user = user
        @request = request
        @params = ActionController::Parameters.new(request.params)
        @from = @params.require(:from)
        @document = Document.find(@params[:id])
        @site = Site.find_by(id: @params[:site_id]) || @document.site || @document.documentable&.site
        @global_renderer = GlobalRenderer.new(success: false, options: {})
        params_verification
        from_params_verification
      end

      def call
        document_scope = @params.require(:document).permit(:document_scope)[:document_scope]
        return self unless document_scope.in?(['personal', 'enterprise']) # Ajout d'un retour si scope invalide pour éviter erreur

        available_types = DocumentType.for_site(@site)
        case document_scope
        when 'personal'
          available_types = available_types.person + available_types.participant
        when 'enterprise'
          available_types = available_types.company + available_types.assignment
        else # Ne devrait pas arriver grâce à la vérification précédente
          available_types = []
        end

        current_type = @document.document_type
        document_type_params = {}

        # Si le type actuel n'est pas dans les types disponibles pour le nouveau scope,
        # ou si le scope change et force une réévaluation du type.
        if current_type.nil? || !available_types.include?(current_type)
          first_available = available_types.first
          if first_available
            document_type_params[:document_type_id] = first_available.id
            # On réinitialise documentable seulement si le type change effectivement à cause du scope
            @document.documentable_type = nil
            @document.documentable_id = nil
          else
            # Si aucun type n'est disponible, on pourrait vouloir vider le type actuel aussi
            # ou gérer cette situation comme une erreur. Pour l'instant, on vide.
            document_type_params[:document_type_id] = nil
            @document.documentable_type = nil
            @document.documentable_id = nil
          end
        end
        # Important: On doit aussi mettre à jour le scope dans la table documents si ce champ existe.
        # S'il n'y a pas de champ 'scope' directement sur le modèle Document,
        # cette logique de changement de scope affecte principalement le document_type_id
        # et les documentables.
        # Si un champ @document.scope existe, il faudrait l'updater ici :
        # document_params_to_update[:scope] = document_scope (si pertinent)

        if @document.update(document_type_params)
          @global_renderer.success = true
        else
          @global_renderer.options[:error_message] = "Erreur lors de la mise à jour du scope du document."
          # Si la mise à jour échoue, on ne met pas à jour les partials ou la validité
          return self
        end

        @document.documentable&.update_validity_status # Appelé après la sauvegarde réussie
        set_partials_for_scope_update # Renommé et sera adapté
        return self
      end

      # Liste des sources autorisées pour la mise à jour du document
      ALLOWED_FROM_PARAMS = %w[
        participant_documents_index
        assignment_documents_index
        uploads_index
        uploads_show
        assignments_show
        pending_participants_show
        participants_show
      ].freeze

      private

      # Cette méthode sera adaptée pour refléter les changements dus au scope.
      # Pour l'instant, je la copie telle quelle et nous l'ajusterons.
      def set_partials_for_scope_update
        documentable = @document.documentable&.on_site(@site)
        locals = set_locals(documentable) # set_locals pourrait avoir besoin d'être ajusté si le scope impacte ses paramètres
        partials = case @from
                   when 'uploads_show'
                     [
                       { turbo_action: "remove", id: "extracted-fields-validation-errors-#{@document.id}" },
                       # Le formulaire d'édition pourrait changer si le type de document change
                       { turbo_action: "update", partial: "documents/extracted_fields", id: "document-edit-form-#{@document.id}", locals: { document: @document, site: @site, scope: locals[:scope], status: locals[:status], validation_status: locals[:validation_status], document_validation_status: locals[:document_validation_status] } },
                     ]
                   when 'uploads_index'
                     dataset_for_row = build_uploads_datatable_dataset(locals)
                     [
                       { turbo_action: "remove", id: "extracted-fields-validation-errors-#{@document.id}" },
                       { turbo_action: "update", partial: "documents/extracted_fields", id: "document-edit-form-#{@document.id}", locals: { document: @document, site: @site, scope: locals[:scope], status: locals[:status], validation_status: locals[:validation_status], document_validation_status: locals[:document_validation_status] } },
                       { turbo_action: "update", partial: "documents/document_item", id: "document-item-#{@document.id}", locals: { document: @document, scope: locals[:scope], status: locals[:status] } },
                       { turbo_action: "replace", partial: "shared/data_table/data_table_row", id: "upload_#{@document.upload.id}", locals: { row: @document.upload.reload, site: @site, scope: locals[:scope], status: locals[:status], dataset: dataset_for_row } }
                      ]
                   when 'participant_documents_index', 'assignment_documents_index'
                     [
                       { turbo_action: "remove", id: "extracted-fields-validation-errors-#{@document.id}" },
                       { turbo_action: "update", partial: "documents/extracted_fields", id: "document-edit-form-#{@document.id}", locals: { document: @document, site: @site, scope: locals[:scope], status: locals[:status], validation_status: locals[:validation_status], document_validation_status: locals[:document_validation_status] } },
                       # Si le scope change le type, la liste des documents pourrait devoir être entièrement rechargée
                       # ou au moins l'item spécifique mis à jour de manière plus significative.
                       { turbo_action: "update", partial: "documents/index", id: "documents-index-datatable", locals: { documents: documentable.documents.reload, validation_status: locals[:validation_status], participant: documentable, site: @site } }
                     ]
                   when 'assignments_show', 'pending_participants_show', 'participants_show'
                     [
                       { turbo_action: "remove", id: "extracted-fields-validation-errors-#{@document.id}" },
                       { turbo_action: "update", partial: "documents/extracted_fields", id: "document-edit-form-#{@document.id}", locals: { document: @document, site: @site, scope: locals[:scope], status: locals[:status], validation_status: locals[:validation_status], document_validation_status: locals[:document_validation_status] } },
                       { turbo_action: "update", partial: "concerns/verifiables/documents_status", id: "documents-documents-status-#{documentable.id}", locals: { validation_status: locals[:validation_status] } },
                       { turbo_action: "update", partial: "concerns/verifiables/conditions_status", id: "documents-conditions-status-#{documentable.id}", locals: { validation_status: locals[:validation_status] } }
                     ]
                   else
                     []
                   end
        @global_renderer.options[:partials_config] = partials
      end

      def set_locals(documentable = nil)
        # Le paramètre 'scope' ici vient des params de la requête originale, pas le document_scope qu'on change
        scope_param = @params[:scope] || "user"
        status_param = @params[:status] || "pending"
        if documentable.present?
          validation_status = validation_status(documentable)
          document_validation_status = document_validation_status(validation_status)
        else
          validation_status = {}
          document_validation_status = {}
        end
        return { scope: scope_param, status: status_param, validation_status:, document_validation_status: }
      end

      def validation_status(documentable)
        return { documents: [] } if documentable.nil? || !documentable.respond_to?(:documents)

        DocumentValidationStatusService.new(documentable).call.with_indifferent_access
      end

      def document_validation_status(validation_status)
        validation_status[:documents].find do |doc_status|
          doc_status[:document].id == @document.id
        end
      end

      def build_uploads_datatable_dataset(locals)
        # Cette fonction est spécifique à 'uploads_index', elle peut rester telle quelle pour l'instant.
        dataset = DataTables::MasheDataTable.new
        dataset.checkable = { method: :id }
        dataset.columns = [
          { header: "Statut", method: :status_verbose, formatter: :indicator, sortable: true, classes: ["col-lg-1"] },
          { header: "Nom du fichier", method: :truncated_name, formatter: :turbo_link, format_params: [:site_upload_path, @site, "self", { scope: locals[:scope], status: locals[:status] }], sortable: true, classes: ["col-lg-3", "justify-content-start", "hover-span-cell"] },
          { header: "Importé par", method: :user_full_name, sortable: true, classes: ["col-lg-3"] },
          { header: "Date d'import", method: :created_at, formatter: :date, sortable: true, classes: ["col-lg-2"] },
          { header: "Méthode d'import", method: :import_method_verbose, sortable: true, classes: ["col-lg-1"] }
        ]
        dataset.actions = [
          {
            link: {
              content: "Supprimer",
              path: :site_upload_path,
              args: [@site, "self"],
              kwargs: { scope: locals[:scope], status: locals[:status], from: @from },
              turbo_stream: true,
              turbo_method: :delete,
              turbo_confirm: "Êtes-vous sûr de vouloir supprimer l'importation ? Tous les documents associés seront supprimés."
            }
          }
        ]
        dataset.bulk_actions = [
          { name: "Placeholder Action", target_url: "#", action_type: "turbo" }
        ]
        dataset.build_hash
      end

      def from_params_verification
        return if @from.present? && @from.in?(ALLOWED_FROM_PARAMS)
        raise StandardError, "From not initialized or not a String object in #{self.class}. Ensure @from is set in the initializer."
      end

      def params_verification
        # Vérifie que le document, la requête et le scope sont présents
        required_params = {
          document: @document,
          request: @request,
          document_scope: @params[:document][:document_scope] # Ajout de la vérification pour document_scope
        }
        missing_params = required_params.select { |name, param| param.blank? }

        unless missing_params.empty?
          missing_keys = missing_params.keys.join(', ')
          raise StandardError, "#{missing_keys.capitalize} not initialized or not valid in #{self.class}. Ensure these are set."
        end
      end
    end
  end
end
