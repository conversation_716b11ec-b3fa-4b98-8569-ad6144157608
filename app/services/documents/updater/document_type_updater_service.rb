module Documents
  module Updater
    class DocumentTypeUpdaterService
      attr_reader :global_renderer

      def initialize(user:, request:)
        @user = user
        @request = request
        @params = ActionController::Parameters.new(request.params)
        @from = @params.require(:from)
        @document = Document.find(@params[:id])
        @site = Site.find_by(id: @params[:site_id]) || @document.site || @document.documentable&.site
        @global_renderer = GlobalRenderer.new(success: false, options: {})
        params_verification
        from_params_verification
      end

      def call
        @document_type_params = @params.require(:document).permit(:document_type_id)

        if @document.update(@document_type_params)
          @global_renderer.success = true
        else
          @global_renderer.options[:error_message] = "Erreur lors de la mise à jour du type de document"
        end
        @document.documentable&.update_validity_status
        set_partials
        return self
      end

      # Liste des sources autorisées pour la mise à jour du document
      ALLOWED_FROM_PARAMS = %w[
        participant_documents_index
        assignment_documents_index
        uploads_index
        uploads_show
        assignments_show
        pending_participants_show
        participants_show
      ].freeze

      private

      def set_locals(documentable = nil)
        scope = @params[:scope] || "user"
        status = @params[:status] || "pending"
        if documentable.present?
          validation_status = validation_status(documentable)
          document_validation_status = document_validation_status(validation_status)
        else
          validation_status = {}
          document_validation_status = {}
        end
        return { scope:, status:, validation_status:, document_validation_status: }
      end

      def set_partials
        documentable = @document.documentable&.on_site(@site)
        locals = set_locals(documentable)
        partials = case @from
                   when 'uploads_show'
                     [
                       { turbo_action: "remove", id: "extracted-fields-validation-errors-#{@document.id}" },
                       { turbo_action: "update", partial: "documents/extracted_fields", id: "document-edit-form-#{@document.id}", locals: { document: @document, site: @site, scope: locals[:scope], status: locals[:status], validation_status: locals[:validation_status], document_validation_status: locals[:document_validation_status] } },
                     ]
                   when 'uploads_index'
                     dataset_for_row = build_uploads_datatable_dataset(locals)
                     [
                       { turbo_action: "remove", id: "extracted-fields-validation-errors-#{@document.id}" },
                       { turbo_action: "update", partial: "documents/extracted_fields", id: "document-edit-form-#{@document.id}", locals: { document: @document, site: @site, scope: locals[:scope], status: locals[:status], validation_status: locals[:validation_status], document_validation_status: locals[:document_validation_status] } },
                       { turbo_action: "update", partial: "documents/document_item", id: "document-item-#{@document.id}", locals: { document: @document, scope: locals[:scope], status: locals[:status] } },
                       { turbo_action: "replace", partial: "shared/data_table/data_table_row", id: "upload_#{@document.upload.id}", locals: { row: @document.upload.reload, site: @site, scope: locals[:scope], status: locals[:status], dataset: dataset_for_row } }
                      ]
                   when 'participant_documents_index', 'assignment_documents_index'
                     [
                       { turbo_action: "remove", id: "extracted-fields-validation-errors-#{@document.id}" },
                       { turbo_action: "update", partial: "documents/extracted_fields", id: "document-edit-form-#{@document.id}", locals: { document: @document, site: @site, scope: locals[:scope], status: locals[:status], validation_status: locals[:validation_status], document_validation_status: locals[:document_validation_status] } },
                       { turbo_action: "update", partial: "documents/index", id: "documents-index-datatable", locals: { documents: documentable.documents.reload, validation_status: locals[:validation_status], participant: documentable, site: @site } }
                     ]
                   when 'assignments_show', 'pending_participants_show', 'participants_show'
                     [
                       { turbo_action: "remove", id: "extracted-fields-validation-errors-#{@document.id}" },
                       { turbo_action: "update", partial: "documents/extracted_fields", id: "document-edit-form-#{@document.id}", locals: { document: @document, site: @site, scope: locals[:scope], status: locals[:status], validation_status: locals[:validation_status], document_validation_status: locals[:document_validation_status] } },
                       { turbo_action: "update", partial: "concerns/verifiables/documents_status", id: "documents-documents-status-#{documentable.id}", locals: { validation_status: locals[:validation_status] } },
                       { turbo_action: "update", partial: "concerns/verifiables/conditions_status", id: "documents-conditions-status-#{documentable.id}", locals: { validation_status: locals[:validation_status] } }
                     ]
                   else
                     []
                   end
        @global_renderer.options[:partials_config] = partials
      end

      def validation_status(documentable)
        return { documents: [] } if documentable.nil? || !documentable.respond_to?(:documents)

        DocumentValidationStatusService.new(documentable).call.with_indifferent_access
      end

      def document_validation_status(validation_status)
        validation_status[:documents].find do |doc_status|
          doc_status[:document].id == @document.id
        end
      end

      def build_uploads_datatable_dataset(locals)
        dataset = DataTables::MasheDataTable.new
        dataset.checkable = { method: :id }
        dataset.columns = [
          { header: "Statut", method: :status_verbose, formatter: :indicator, sortable: true, classes: ["col-lg-1"] },
          { header: "Nom du fichier", method: :truncated_name, formatter: :turbo_link, format_params: [:site_upload_path, @site, "self", { scope: locals[:scope], status: locals[:status] }], sortable: true, classes: ["col-lg-3", "justify-content-start", "hover-span-cell"] },
          { header: "Importé par", method: :user_full_name, sortable: true, classes: ["col-lg-3"] },
          { header: "Date d'import", method: :created_at, formatter: :date, sortable: true, classes: ["col-lg-2"] },
          { header: "Méthode d'import", method: :import_method_verbose, sortable: true, classes: ["col-lg-1"] }
        ]
        dataset.actions = [
          {
            link: {
              content: "Supprimer",
              path: :site_upload_path,
              args: [@site, "self"],
              kwargs: { scope: locals[:scope], status: locals[:status], from: @from },
              turbo_stream: true,
              turbo_method: :delete,
              turbo_confirm: "Êtes-vous sûr de vouloir supprimer l'importation ? Tous les documents associés seront supprimés."
            }
          }
        ]
        dataset.bulk_actions = [
          { name: "Placeholder Action", target_url: "#", action_type: "turbo" }
        ]
        dataset.build_hash
      end

      def from_params_verification
        return if @from.present? && @from.in?(ALLOWED_FROM_PARAMS)

        raise StandardError, "From not initialized or not a String object in #{self.class}. Ensure @from is set in the initializer."
      end

      def params_verification
        return if @document.present? && @request.present?

        raise StandardError, "Document or request not initialized or not a Document object in #{self.class}. Ensure @document or @request is set in the initializer."
      end
    end
  end
end
