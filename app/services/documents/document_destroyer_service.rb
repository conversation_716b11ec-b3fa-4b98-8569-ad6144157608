module Documents
  class DocumentDestroyerService
    attr_reader :global_renderer

    def initialize(user:, request:)
      @user = user
      @request = request
      @params = ActionController::Parameters.new(request.params)
      @from = @params.require(:from)
      @document = Document.find(@params[:id])
      @site = Site.find_by(id: @params[:site_id]) || @document.site || @document.documentable&.site
      @global_renderer = GlobalRenderer.new(success: false, options: {})
      params_verification
      from_params_verification
    end

    def call
      @documentable = @document.documentable
      @document_id = @document.id
      @upload = @document.upload

      perform_destroy

      if @global_renderer.success?
        @documentable&.update_validity_status
        set_partials
        @global_renderer.options[:success_message] ||= "Document supprimé avec succès."
      else
        @global_renderer.options[:error_message] ||= "Impossible de supprimer ce document."
      end

      return self
    end

    # Liste des sources autorisées pour la mise à jour du document
    ALLOWED_FROM_PARAMS = %w[
      participant_documents_index
      assignment_documents_index
      uploads_index
      uploads_show
      assignments_show
      pending_participants_show
      participants_show
    ].freeze

    private

    def perform_destroy
      if @document.destroy
        @global_renderer.success = true
      else
        @global_renderer.options[:error_message] = @document.errors.full_messages.join(", ")
      end
    end

    def set_partials
      documentable = @documentable&.on_site(@site)
      locals = set_locals(documentable)
      partials = case @from
                 when 'uploads_show'
                   [
                     { turbo_action: "remove", id: "upload-show-document-#{@document_id}" },
                   ]
                 when 'uploads_index'
                   [
                     { turbo_action: "remove", id: "document-item-#{@document_id}" },
                     { turbo_action: "update", id: "modal-container", partial: "" },
                     { turbo_action: "update", partial: "uploads/index", id: "uploads-index-datatable-container", locals: { uploads: locals[:uploads], site: @site, scope: locals[:scope], status: locals[:status] } }
                   ]
                 when 'participant_documents_index', 'assignment_documents_index'
                   [
                     { turbo_action: "update", id: "modal-container", partial: "" },
                     { turbo_action: "update", id: "documents-count-#{documentable.id}", partial: "documents/document_count", locals: { count: locals[:count] } },
                     { turbo_action: "update", partial: "documents/index", id: "documents-index-datatable", locals: { documents: documentable.documents, validation_status: locals[:validation_status], participant: documentable, site: @site } }
                   ]
                 when 'assignments_show', 'pending_participants_show', 'participants_show'
                   [
                     { turbo_action: "update", id: "modal-container", partial: "" },
                     { turbo_action: "update", partial: "concerns/verifiables/documents_status", id: "documents-documents-status-#{documentable.id}", locals: { validation_status: locals[:validation_status], from: @from } },
                     { turbo_action: "update", partial: "concerns/verifiables/conditions_status", id: "documents-conditions-status-#{documentable.id}", locals: { validation_status: locals[:validation_status] } },
                   ]
                 else
                   []
                 end
      @global_renderer.options[:partials_config] = partials
    end

    def set_locals(documentable)
      scope = @params[:scope] || "user"
      status = @params[:status] || "pending"
      validation_status = {}
      document_validation_status = {}
      if documentable.present?
        validation_status = validation_status(documentable)
        document_validation_status = document_validation_status(validation_status)
      end

      case @from
      when 'uploads_index', 'uploads_show'
        { scope:, status:, validation_status:, document_validation_status:, uploads: set_uploads(scope, status) }
      when 'participant_documents_index', 'assignment_documents_index', 'assignments_show', 'pending_participants_show', 'participants_show'
        { scope: scope, status: status, validation_status:, document_validation_status:, from: @from, count: documentable.documents.count }
      else
        {}
      end
    end

    def set_uploads(scope, status)
      scope = case scope
              when "company" then @user.company
              when "all" then :all
              else @user
              end
      Perimeters::UserPerimeter.new(site: @site, user: @user)
                               .uploads_scope(
                                 status: status == "validated" ? :validated : %i[pending processing failed completed],
                                 scope: scope,
                                 include_documents: true
                               ).order(created_at: :desc)
    end

    def validation_status(documentable)
      return { documents: [] } if documentable.nil? || !documentable.respond_to?(:documents)

      DocumentValidationStatusService.new(documentable).call.with_indifferent_access
    end

    def document_validation_status(validation_status)
      validation_status[:documents].find do |doc_status|
        doc_status[:document].id == @document.id
      end
    end

    def from_params_verification
      return if @from.present? && @from.in?(ALLOWED_FROM_PARAMS)

      raise StandardError, "From not initialized or not a String object in #{self.class}. Ensure @from is set in the initializer."
    end

    def params_verification
      return if @document.present? && @request.present?

      raise StandardError, "Document or request not initialized or not a Document object in #{self.class}. Ensure @document or @request is set in the initializer."
    end
  end
end
