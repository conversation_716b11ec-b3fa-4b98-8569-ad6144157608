# Service pour résoudre les noms de buckets selon l'environnement
class StorageBucketResolver
  # Résout le bucket pour le stockage principal (photos, images, etc.)
  def self.storage_bucket
    resolve_bucket('STORAGE', 'mashe-storage')
  end

  # Résout le bucket pour les uploads
  def self.uploads_bucket
    resolve_bucket('UPLOADS', 'mashe-fold-uploads')
  end

  # Résout le bucket pour les documents
  def self.documents_bucket
    resolve_bucket('DOCUMENTS', 'mashe-fold-documents')
  end

  private

  # Résout un bucket selon l'environnement avec fallback intelligent
  def self.resolve_bucket(type, production_default)
    case Rails.env
    when 'development'
      ENV.fetch("OUTSCALE_#{type}_DEVELOPMENT_BUCKET", "#{production_default}-development")
    when 'sandbox', 'demo'
      ENV.fetch("OUTSCALE_#{type}_SANDBOX_BUCKET", "#{production_default}-sandbox")
    else
      ENV.fetch("OUTSCALE_#{type}_BUCKET", production_default)
    end
  end
end
