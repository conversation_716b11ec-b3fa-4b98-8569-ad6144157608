module Companies
  class Searcher
    def initialize(query:, interim: nil)
      @query = query
      @interim = ActiveModel::Type::Boolean.new.cast(interim)
    end

    def search
      return [] if @query.blank?

      results = deduplicate_results(
        search_in_database,
        search_in_pappers
      )
      @interim ? filter_interim(results) : results
    end

    private

    def search_in_database
      Company
        .search(@query)
        .limit(10)
        .map { |company| format_database_result(company) }
    end

    def search_in_pappers
      Apis::Pappers
        .new(@query)
        .search
        .map { |result| format_pappers_result(result) }
    end

    def format_database_result(company)
      {
        id: company.id,
        name: company.name,
        company_number: company.company_number,
        interim: company.interim,
        address: company.address,
        source: :database
      }
    end

    def format_pappers_result(result)
      {
        id: nil,
        name: result[:name],
        company_number: result[:company_number],
        interim: result[:interim],
        address: result[:address],
        source: :pappers
      }
    end

    def deduplicate_results(db_results, pappers_results)
      (db_results + pappers_results)
        .group_by { |result| result[:company_number] }
        .values
        .map { |duplicates| duplicates.find { |r| r[:source] == :database } || duplicates.first }
        .compact
    end

    def filter_interim(results)
      results.select { |result| result[:interim] == @interim } unless @interim.nil?
    end
  end
end
