# Service for consistent storage service references
class StorageService
  # Returns the main storage service for general attachments (photos, images, etc.)
  def self.storage_service
    :outscale_storage
  end

  # Returns the uploads service for file uploads
  def self.uploads_service
    :outscale_uploads
  end

  # Returns the documents service for processed documents
  def self.documents_service
    :outscale_documents
  end

  # Convenience method for backward compatibility
  def self.storage_service_for_environment
    storage_service
  end
end
