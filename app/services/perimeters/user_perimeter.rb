module Perimeters
  class UserPerimeter
    def initialize(user:, site:)
      @user = user
      @site = site
      @user_site = UserSite.find_by(user: @user, site: @site)
      validate!
    end

    def assignments_scope
      return site_assignments if admin_or_view_all_assignments_or_participants_permission?
      return assignments_with_own_people if interim_or_view_with_own_people_permission?
      return own_assignments_with_children if view_own_assignments_or_participants_permission?

      company_site_assignments
    end

    def companies_scope
      Company.where(id: assignments_scope.pluck(:company_id).uniq)
    end

    def participants_scope
      assignment_ids = assignments_scope.pluck(:id)
      return Participant.none if assignment_ids.empty?

      Participant.where(assignment_id: assignment_ids)
    end

    def pending_participants_scope
      base_assignments_scope = assignments_scope
      assignment_ids = base_assignments_scope.pluck(:id)
      company_ids = base_assignments_scope.pluck(:company_id).uniq
      return PendingParticipant.none if assignment_ids.empty?

      PendingParticipant.where(assignment_id: assignment_ids)
                        .or(PendingParticipant.where(company_id: company_ids, site_id: @site.id))
                        .or(PendingParticipant.where(user: @user.company.users, site_id: @site.id))
    end

    def uploads_scope(status:, scope: :all, include_documents: false)
      companies_ids = assignments_scope.pluck(:company_id).uniq
      users_ids = User.where(company_id: companies_ids).pluck(:id)
      uploads = Upload.includes(:user).where(user_id: users_ids, site_id: @site.id)
      uploads = uploads.includes(:documents) if include_documents

      apply_filters_to_uploads(uploads, scope, status)
    end

    private

    def apply_filters_to_uploads(uploads, scope, status)
      uploads = filter_uploads_by_scope(uploads, scope)
      filter_uploads_by_status(uploads, status)
    end

    def filter_uploads_by_scope(uploads, scope)
      case scope
      when :all
        uploads
      when ->(s) { s.is_a?(User) }
        uploads.where(user_id: scope.id)
      when ->(s) { s.is_a?(Company) }
        users_ids = scope.users.pluck(:id)
        uploads.where(user_id: users_ids)
      else
        raise ArgumentError, "Scope non supporté: #{scope.class.name}"
      end
    end

    def filter_uploads_by_status(uploads, status_param)
      # processed_statuses = Array(status_param)

      # if processed_statuses.empty?
      #   raise ArgumentError, "La liste de statuts ne peut pas être vide."
      # end

      # actual_query_statuses = []
      # apply_not_validated_filter = false

      # processed_statuses.each do |s|
      #   case s
      #   when :validated
      #     actual_query_statuses << :validated
      #   when :pending
      #     apply_not_validated_filter = true
      #   when :processing
      #     actual_query_statuses << :processing
      #   when :failed
      #     actual_query_statuses << :failed
      #   else
      #     raise ArgumentError, "Statut non supporté dans la liste: #{s}"
      #   end
      # end
      # actual_query_statuses.uniq!

      # final_scope = uploads # Start with the base scope

      # if apply_not_validated_filter
      #   condition_not_validated = uploads.where.not(status: :validated)

      #   if actual_query_statuses.any?
      #     # Combine "not validated" with other specific statuses using OR
      #     # e.g., status_param = [:pending, :failed] results in (status != :validated) OR (status = :failed)
      #     # e.g., status_param = [:pending, :validated] results in (status != :validated) OR (status = :validated), effectively all uploads matching other criteria
      #     condition_actual_statuses = uploads.where(status: actual_query_statuses)
      #     final_scope = condition_not_validated.or(condition_actual_statuses)
      #   else
      #     # Only :pending was specified
      #     final_scope = condition_not_validated
      #   end
      # elsif actual_query_statuses.any?
      #   # Only specific statuses were specified, no :pending
      #   final_scope = uploads.where(status: actual_query_statuses)
      # end

      # final_scope

      raw_statuses = status_param.is_a?(Array) ? status_param : [status_param]
      raise ArgumentError, "Statut non supporté: #{raw_statuses}" if raw_statuses.empty? || raw_statuses.any? { |s| !Upload.statuses.key?(s) }

      uploads.where(status: raw_statuses)
    end

    def admin_or_view_all_assignments_or_participants_permission?
      @user.admin? || %i[view_all_assignments view_all_participants].any? { |perm| @user_site&.permission?(perm) }
    end

    def interim_or_view_with_own_people_permission?
      (@user_site.present? && @user_site.role?(:interim)) || %i[view_assignements_with_own_people
                                                                view_participants_with_own_people].any? do |perm|
        @user_site&.permission?(perm)
      end
    end

    def view_own_assignments_or_participants_permission?
      %i[view_own_assignments view_own_participants].any? { |perm| @user_site&.permission?(perm) }
    end

    def site_participants
      Participant.joins(:assignment).where(assignments: { site_id: @site.id })
    end

    def participants_with_own_people
      people_ids = @user.company.people.pluck(:id)
      return Participant.none if people_ids.empty?

      Participant.where(person_id: people_ids)
                 .joins(:assignment)
                 .where(assignments: { site_id: @site.id })
    end

    def own_participants
      people_ids = @user.company.people.pluck(:id)
      return Participant.none if people_ids.empty?

      Participant.where(person_id: people_ids)
                 .joins(:assignment)
                 .where(assignments: { site_id: @site.id })
    end

    def site_assignments
      Assignment.where(site: @site)
    end

    def assignments_with_own_people
      people_ids = @user.company.people.pluck(:id)
      return Assignment.none if people_ids.empty?

      Assignment.joins(:participants)
                .where(participants: { person_id: people_ids })
                .where(site: @site)
                .distinct
    end

    def own_assignments_with_children
      user_assignments = Assignment.where(site: @site, company: @user.company)
      return Assignment.none if user_assignments.empty?

      assignment_ids = user_assignments.pluck(:id) + fetch_children_assignments(user_assignments).pluck(:id)
      Assignment.where(id: assignment_ids)
    end

    def company_site_assignments
      Assignment.where(company: @user.company, site: @site)
    end

    def fetch_children_assignments(assignments)
      children = Assignment.where(assignment_parent: assignments)
      return Assignment.none if children.empty?

      children_ids = children.pluck(:id)
      grandchildren = fetch_children_assignments(children)

      Assignment.where(id: children_ids + grandchildren.pluck(:id))
    end

    def validate!
      raise ArgumentError, "user is required" if @user.blank?
      raise ArgumentError, "site is required" if @site.blank?
      return if @user.admin? || (@user_site.present? && @user_site.role?(:interim))

      raise ActiveRecord::RecordNotFound, "user_site not found" if @user_site.blank?
    end
  end
end
