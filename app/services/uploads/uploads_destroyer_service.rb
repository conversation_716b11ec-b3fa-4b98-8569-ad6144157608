module Uploads
  class UploadsDestroyerService
    attr_reader :global_renderer

    def initialize(user:, request:)
      @user = user
      @request = request
      @params = ActionController::Parameters.new(request.params)
      @from = @params.require(:from)
      @site = Site.find_by(id: @params[:site_id]) || @document.site || @document.documentable&.site
      @global_renderer = GlobalRenderer.new(success: false, options: {})
      from_params_verification
    end

    def call
      @uploads = set_destroyables
      @upload_ids = @uploads.pluck(:id)
      perform_destroy

      if @global_renderer.success?
        @documentable&.update_validity_status
        set_partials
        @global_renderer.options[:success_message] ||= "Import(s) supprimé avec succès."
      else
        @global_renderer.options[:error_message] ||= "Impossible de supprimer ce(s) import(s)."
      end

      return self
    end

    # Liste des sources autorisées pour la mise à jour du document
    ALLOWED_FROM_PARAMS = %w[
      uploads_index
    ].freeze

    private

    def set_destroyables
      raise ArgumentError, "No upload ID or IDs list provided" if @params[:id].blank? && @params[:ids_list].blank?

      uploads = Upload.where(id: @params[:id]) if @params[:id].present?
      uploads = Upload.where(id: @params[:ids_list]) if @params[:ids_list].present? && @params[:ids_list].is_a?(Array)
      raise NotFoundError, "No uploads found" if uploads.blank?
      uploads
    end

    def perform_destroy
      if @uploads.destroy_all
        @global_renderer.success = true
      else
        @global_renderer.options[:error_message] = "Une erreur est survenue lors de la suppression des documents."
      end
    end

    def set_partials
      partials = case @from
                 when 'uploads_index'
                   temp = [
                     { turbo_action: "update", id: "modal-container", partial: "" },
                     { turbo_action: "update", id: "sidebar-container", partial: "" }
                   ]
                   @upload_ids.each do |upload_id|
                     temp << { turbo_action: "remove", id: "upload_#{upload_id}" }
                   end
                   temp
                 else
                   []
                 end
      @global_renderer.options[:partials_config] = partials
    end

    def from_params_verification
      return if @from.present? && @from.in?(ALLOWED_FROM_PARAMS)

      raise StandardError, "From not initialized or not a String object in #{self.class}. Ensure @from is set in the initializer."
    end
  end
end
