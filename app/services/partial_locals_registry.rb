class PartialLocalsRegistry
  # Génère les variables locales pour un partial donné
  # @param partial_id [String] Identifiant du partial
  # @param context [Hash] Contexte d'exécution (objets nécessaires pour générer les locals)
  # @return [Hash] Variables locales à passer au partial

  # Génère les variables locales pour un partial donné
  REGISTRY = {
    "documents_status" => :documents_status_locals,
    "conditions_status" => :conditions_status_locals,
    "document_upload_zones" => :documents_upload_zones_locals,
    "extracted-fields-container" => :extracted_fields_form_locals,
    "documents/extracted_fields" => :extracted_fields_form_locals,
    "documents/extracted_fields_errors" => :extracted_fields_errors_locals,
    "concerns/verifiables/documents_status" => :documents_status_locals,
    "concerns/verifiables/conditions_status" => :conditions_status_locals,
    "documents/document_item" => :document_item_locals,
    "uploads-index-datatable-container" => :uploads_index_datatable_container_locals,
    "documents-index-datatable" => :documents_index_datatable_locals,
    "documents/documentable_details" => :documentable_details_locals,
    "documents/document_count" => :document_count_locals
  }.freeze

  def self.generate_locals(partial_path, partial_id, context)
    context = context.with_indifferent_access

    # Chercher d'abord par partial_path, puis par partial_id
    registry_key = if REGISTRY.key?(partial_path)
                     partial_path
                   elsif REGISTRY.key?(partial_id)
                     partial_id
                   end

    if registry_key
      send(REGISTRY[registry_key], context)
    else
      # Pour les partiels inconnus, on retourne juste le contexte
      context
    end
  end

  # Génère les locals pour le partial documents_status
  # @param context [Hash] Contexte avec :documentable et :site
  # @return [Hash] Variables locales pour le partial
  def self.documents_status_locals(context)
    documentable = context[:before_update_documentable] || context[:documentable]
    validation_status = validation_status(documentable, context[:site])

    {
      documentable: documentable,
      validation_status: validation_status
    }
  end

  def self.documents_upload_zones_locals(context)
    documentable = context[:documentable].on_site(context[:site])
    role = documentable.respond_to?(:role_context) ? documentable.role_context : documentable.assignment_role_site

    puts "\n\nrole: #{role.inspect}\n\n"
    document_types_config = RoleDocumentTypesQuery.new(role).call
    needed_document_types = document_types_config.map { |config| config[:document_type] }.uniq
    puts "\n\nneeded_document_types: #{needed_document_types.count}\n\n"

    {
      documentable: documentable,
      site: context[:site],
      needed_document_types: needed_document_types
    }
  end

  # Génère les locals pour le partial conditions_status
  # @param context [Hash] Contexte avec :documentable et :site
  # @return [Hash] Variables locales pour le partial
  def self.conditions_status_locals(context)
    documentable = context[:documentable].on_site(context[:site])
    validation_status = validation_status(documentable, context[:site])

    {
      documentable: documentable,
      validation_status: validation_status
    }
  end

  def self.extracted_fields_form_locals(context)
    scope = context[:scope] || "all"
    status = context[:status] || "pending"
    if context[:before_update_documentable].present? || context[:documentable].present?
      validation_status = validation_status(context[:before_update_documentable] || context[:documentable], context[:site])
      document_validation_status = document_validation_status(validation_status, context[:document])
    end

    data = {
      document: context[:document],
      site: context[:site],
      scope: scope,
      status: status
    }
    data[:validation_status] = validation_status if validation_status.present?
    data[:document_validation_status] = document_validation_status if document_validation_status.present?
    data
  end

  def self.extracted_fields_errors_locals(context)
    validation_status = validation_status(context[:documentable], context[:site])
    document_validation_status = document_validation_status(validation_status, context[:document])
    {
      document: context[:document],
      document_validation_status: document_validation_status
    }
  end

  def self.document_item_locals(context)
    scope = context[:scope] || "all"
    status = context[:status] || "pending"
    {
      document: context[:document],
      scope: scope,
      status: status
    }
  end

  def self.uploads_index_datatable_container_locals(context)
    scope = context[:scope] || "user"
    status = context[:status] || "pending"
    uploads = set_uploads(scope, status, context[:site], context[:user])
    {
      scope: scope,
      status: status,
      uploads: uploads
    }
  end

  def self.documents_index_datatable_locals(context)
    documentable = context[:before_update_documentable] || context[:documentable]
    validation_status = validation_status(documentable, context[:site])
    {
      documents: documentable.documents,
      validation_status: validation_status
    }
  end

  def self.documentable_details_locals(context)
    {
      document: context[:document]
    }
  end

  def self.document_count_locals(context)
    {
      count: context[:documentable]&.documents&.count || 0
    }
  end

  def self.set_uploads(scope, status, site, user)
    scope = case scope
            when "company" then user.company
            when "all" then :all
            else user
            end
    Perimeters::UserPerimeter.new(site: site, user: user)
                             .uploads_scope(
                               status: status == "validated" ? :validated : %i[pending processing failed completed],
                               scope: scope
                             ).order(created_at: :desc)
  end

  def self.validation_status(documentable, site)
    return { documents: [] } if documentable.blank? || !documentable.respond_to?(:documents)

    documentable = documentable.on_site(site) if documentable.respond_to?(:on_site)
    DocumentValidationStatusService.new(documentable).call
  end

  def self.document_validation_status(validation_status, document)
    validation_status[:documents].find do |doc_status|
      doc_status[:document].id == document.id
    end
  end
end
