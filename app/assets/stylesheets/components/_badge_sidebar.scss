@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }

  10%,
  40% {
    transform: translateY(-10px);
  }

  30%,
  60% {
    transform: translateY(-5px);
  }
}

#badge-sidebar {
  position: fixed;
  right: 0;
  top: 0;
  width: 400px;
  height: 100%;
  background-color: white;
  border-left: 3px solid $primary-color;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  z-index: 1100;
  transform: translateX(100%);
  transition: transform 0.3s ease, opacity 0.3s ease;
  display: none;
  flex-direction: column;


  &.visible {
    z-index: 1100;
    transform: translateX(0);
    display: flex;
  }

  &[style*="display: flex"] {
    i {
      animation: bounce 3s ease;
    }
  }

  .badge-tab {
    position: absolute;
    left: -40px;
    top: 50%;
    transform: translateY(45%);
    width: 40px;
    height: 100px;
    background-color: white;
    border-radius: 8px 0 0 8px;
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-right: 1px solid $primary-color;
    border-left: 3px solid $primary-color;
    border-bottom: 3px solid $primary-color;
    border-top: 3px solid $primary-color;


    &:hover {
      background-color: darken(white, 5%);
    }

    i {
      font-size: 1.2rem;
      color: $primary-color;
    }
  }

  .badge-header {
    padding: 1.5rem;
    background-color: $primary-color;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: white;
      text-transform: uppercase;
      margin: 0;
    }

    .close-badge {
      background: none;
      border: none;
      font-size: 1.5rem;
      color: white;
      cursor: pointer;
      padding: 0.5rem;
      transition: opacity 0.2s ease;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .badge-actions {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 1rem;
  }

  .badge-content {
    flex: 1;
    overflow-y: auto;
    padding: 0.5rem 0;
    max-height: calc(100vh - 200px); // Hauteur maximale en soustrayant header et actions
  }
}

/* Carte de demande de badge */
.badge-request-card {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  margin: 0.5rem;
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  transition: box-shadow 0.2s ease-in-out;
  cursor: pointer;
  min-height: 70px; // Hauteur minimale fixe pour éviter la déformation
}

.badge-request-card:hover {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Photo de profil */
.badge-request-photo {
  flex-shrink: 0; // Empêche la photo de se rétrécir
  margin-right: 1rem;
}

.badge-request-photo img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

/* Informations du participant */
.badge-request-info {
  flex-grow: 1;
  min-width: 0;
  overflow: hidden;
}

.badge-request-name {
  font-weight: bold;
  color: #343a40;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.badge-request-company {
  font-size: 0.875rem;
  color: #6c757d;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* Checkbox */
.badge-request-checkbox {
  flex-shrink: 0; // Empêche la checkbox de se rétrécir
  margin-left: 1rem;
}

.badge-request-checkbox input {
  margin: 0; // Supprime la marge par défaut
}

/* Style pour les badges avec participants invalides */
.badge-invalid-participant {
  background-color: rgba(220, 53, 69, 0.05);
  border-left: 3px solid #dc3545;
  opacity: 0.8;
}

.badge-invalid-participant .badge-request-checkbox input:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.badge-invalid-participant .text-danger {
  color: #dc3545;
  font-size: 0.8rem;
  font-weight: 500;
  margin-top: 0.3rem;
}

/* Style pour les badges invalides mais forçables */
.badge-invalid-participant input[type="checkbox"]:not(:disabled) {
  accent-color: #dc3545;
  border: 1px solid #dc3545;
  position: relative;
}

.badge-invalid-participant input[type="checkbox"]:not(:disabled):hover {
  cursor: pointer;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.25);
}