.document-edit-form {
  .document-scope-selector {
    display: flex;
    gap: 1rem;
    // margin-bottom: 1.5rem;

    .form-check-input:checked {
      background-color: $primary-color;
      border-color: $primary-color;
    }

    .form-check-label {
      font-weight: 500;
    }
  }

  // Conteneur pour les champs extraits
  .extracted-fields-container {
    .extracted-fields-list {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 1rem;

      // Pour les petits écrans, revenir à une colonne
      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }

      // Style pour chaque champ extrait
      .extracted-field {

        // Si un champ doit prendre toute la largeur
        &.full-width {
          grid-column: 1 / -1;
        }

        // Style des labels
        label {
          font-weight: 500;
          margin-bottom: 0rem;
          font-size: 0.9rem;
          color: #495057;
        }

        // Styles améliorés pour les inputs
        .form-control, .form-select {
          border-radius: 0.35rem;
          border: 1px solid #ced4da;
          padding: 0.375rem 0.75rem;
          font-size: 0.95rem;
          transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

          &:focus {
            border-color: $primary-color;
            box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.25);
          }
        }

        // Style des checkboxes pour maintenir la cohérence
        .form-check-input {
          margin-top: 0rem;
          width: 16px !important;
          height: 16px !important;

          &:checked {
            background-color: $primary-color;
            border-color: $primary-color;
          }
        }

        // Assurer que les inputs prennent toute la largeur disponible
        input, select, textarea {
          width: 100%;
        }

        // Exception pour les checkboxes
        input[type="checkbox"] {
          width: auto;
        }
      }
    }
  }
}

// Animation pour les changements de champs
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.extracted-field {
  animation: fadeIn 0.3s ease-in-out;
}
