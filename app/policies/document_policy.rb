class DocumentPolicy < ApplicationPolicy
  def index?
    permission?(%i[manage_documents manage_own_documents validate_documents view_documents], :or)
  end

  def serve?
    true
  end

  def edit?
    permission?(%i[manage_documents manage_own_documents validate_documents], :or)
  end

  def update?
    edit?
  end

  def create_batch?
    edit?
  end

  def destroy?
    edit?
  end

  def document_history?
    index?
  end

  def extracted_fields?
    index?
  end

  def archive?
    edit?
  end
end
