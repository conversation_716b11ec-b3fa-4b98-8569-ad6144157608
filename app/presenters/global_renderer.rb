class GlobalRenderer
  include Turbo::StreamsHelper
  attr_accessor :success, :options

  def initialize(success:, options: {})
    @success = success
    @options = options
  end

  def success?
    @success
  end

  def render_turbo_stream(view_context)
    raise ArgumentError, "Le paramètre partials_config est obligatoire" if @options[:partials_config].blank?
    raise ArgumentError, "Le paramètre view_context est obligatoire (à envoyer depuis le controller)" if view_context.blank?

    stream_actions = []
    builder = Turbo::Streams::TagBuilder.new(view_context)

    @options[:partials_config].each do |partial|
      check_turbo_params(partial)
      locals = partial[:locals] || {}
      if partial[:turbo_action] == "remove"
        stream_actions << builder.remove(partial[:id])
      elsif partial[:partial] == ""
        # Cas spécial : partial vide signifie vider le contenu de l'élément
        stream_actions << builder.send(partial[:turbo_action], partial[:id], "")
      else
        stream_actions << builder.send(partial[:turbo_action], partial[:id], partial: partial[:partial], locals: locals)
      end
    end

    # Add flash message stream if a message is present
    if @options[:success_message].present? || @options[:error_message].present?
      locals = {}
      locals[:notice] = @options[:success_message] if @options[:success_message].present?
      locals[:alert] = @options[:error_message] if @options[:error_message].present?

      stream_actions << builder.append(
        "flashes-container",
        partial: "shared/flashes",
        locals:
      )
    end

    stream_actions
  end

  private

  def check_turbo_params(partial)
    raise ArgumentError, "Le paramètre turbo_action est obligatoire" if partial[:turbo_action].blank?
    raise ArgumentError, "Le paramètre id est obligatoire" if partial[:id].blank?
    raise ArgumentError, "Le paramètre partial est obligatoire" if partial[:partial].nil? && partial[:turbo_action] != "remove"
  end
end
