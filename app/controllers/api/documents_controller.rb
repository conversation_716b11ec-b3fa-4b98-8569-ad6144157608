module Api
  class DocumentsController < ApplicationController
    def archive
      authorize Document, :archive?
      @documents = Document.where(id: params[:ids_list])
      @documentable = @documents.first.documentable

      success = @documents.all?(&:archive!)

      respond_to do |format|
        format.json do
          if success
            render json: { reload: true }
          else
            render json: { reload: false }, status: :unprocessable_entity
          end
        end
      end
    end
  end
end
