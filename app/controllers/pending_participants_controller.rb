class PendingParticipantsController < ApplicationController
  include TurboUpdatable
  include PhotoAttachable

  before_action :set_pending_participant, only: %i[edit update update_image convert]
  before_action :set_site, only: %i[index new create edit update update_image convert]
  before_action :set_info, only: %i[index new create edit update update_image convert]
  before_action :load_document_types, only: %i[edit update update_image]
  before_action :sync_assignment_with_company, only: %i[create update]

  VALIDITY_ATTRS = PendingParticipant::VALIDITY_ATTRS

  def index
    # TODO : gérer le scope des participants (pour les sous-traitants : avec company du sous traitant ou assignment du sous-traitant)
    @pending_participants = policy_scope(PendingParticipant)

    @failed_records_by_group = FailedConditionRecord
                               .joins(:group_result)
                               .where(group_result: { subject: @pending_participants })
                               .order(failed_at: :desc)
                               .group_by(&:group_result_id)
  end

  def new
    @pending_participant = PendingParticipant.new(site: @site,
                                                  company: current_user.company,
                                                  participant_role_site: @site.default_participant_role_site,
                                                  assignment: Assignment.find_by(
                                                    company_id: current_user.company, site: @site
                                                  ))
    @role_context = @pending_participant.role_context
    @photo_form = params[:photo_form] || true
    @document_types_config = RoleDocumentTypesQuery.new(@role_context).call
    # @document_types_config est maintenant une liste de hash { element:, document_type: }
    @needed_document_types = @document_types_config.map { |config| config[:document_type] }.uniq
    @validation_status = DocumentValidationStatusService.new(@pending_participant).call
    @assignments = Perimeters::UserPerimeter.new(user: current_user, site: @site).assignments_scope
    @employers = Perimeters::UserPerimeter.new(user: current_user, site: @site).companies_scope

    if params[:encoded_partials_config].present?
      partials_config = decode_partials_config(params)
      document = Document.find(params[:document_id])
      return render_turbo_response(
        true,
        partials_config: partials_config,
        context: { participant: @participant, site: @site, assignments: @assignments, employers: @employers,  document: }
      )
    end
  end

  def create
    @pending_participant = PendingParticipant.new(pending_participant_params)
    @pending_participant.site = @site

    # Déterminer automatiquement si c'est un intérimaire basé sur le rôle
    @pending_participant.is_interim = @pending_participant.participant_role_site&.interim_role?

    if @pending_participant.save
      # Traiter la photo si elle a été soumise
      attach_photo(@pending_participant, params, :pending_participant)

      @pending_participant.update_validity_status
      redirect_to edit_site_pending_participant_path(@site, @pending_participant),
                  notice: "Intervenant en attente créé avec succès."
    else
      @role_context = @pending_participant.role_context
      @document_types_config = RoleDocumentTypesQuery.new(@role_context).call
      @needed_document_types = @document_types_config.map { |config| config[:document_type] }.uniq
      @validation_status = DocumentValidationStatusService.new(@pending_participant).call
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    @role_context = @pending_participant.role_context
    @document_types_config = RoleDocumentTypesQuery.new(@role_context).call
    @needed_document_types = @document_types_config.map { |config| config[:document_type] }.uniq
    @validation_status = DocumentValidationStatusService.new(@pending_participant).call
    @assignments = Perimeters::UserPerimeter.new(user: current_user, site: @site).assignments_scope
    @employers = Perimeters::UserPerimeter.new(user: current_user, site: @site).companies_scope
    @current_participant_role_site = @pending_participant.participant_role_site
  end

  def convert
    @pending_participant.convert!(user: current_user)
    redirect_to site_pending_participants_path(@site),
                notice: "Intervenant en attente converti avec succès."
  end

  def update
    # Sauvegarder les valeurs originales avant modification
    original_values = {}
    VALIDITY_ATTRS.each do |attr|
      original_values[attr] = @pending_participant.send(attr)
    end

    @pending_participant.assign_attributes(pending_participant_params)
    @pending_participant.is_interim = @pending_participant.participant_role_site&.interim_role?

    success = false
    if @pending_participant.save
      # Rechargement complet de l'instance avec ses associations
      @pending_participant.reload

      success = true
      update_validity_status_if_needed(original_values)
    end

    partials_config = [
      {
        id: "documents_status",
        partial: "concerns/verifiables/documents_status"
      },
      {
        id: "conditions_status",
        partial: "concerns/verifiables/conditions_status"
      },
      {
        id: "document_upload_zones",
        partial: "concerns/verifiables/document_upload_zones"
      }
    ]

    render_turbo_response(
      success,
      partials_config: partials_config,
      context: { documentable: @pending_participant, site: @site }
    )
  end

  # Mise à jour d'image via AJAX
  def update_image
    # Vérifier les permissions
    authorize @pending_participant

    result, status = handle_image_update(@pending_participant, params, :pending_participant)
    render json: result, status: status
  end

  def destroy
    if params[:ids_list].present?
      @pending_participants = PendingParticipant.where(id: params[:ids_list].map(&:to_i))
      @pending_participants.destroy_all
      flash[:notice] = "Intervenants en attente supprimés avec succès."
      render json: { reload: true }, status: :ok
    else
      @pending_participant = PendingParticipant.find(params[:id])
      @pending_participant.destroy
      redirect_to site_pending_participants_path(@site),
                  notice: "Intervenant en attente supprimé avec succès.",
                  status: :ok
    end
  end

  private

  def set_turbo_stream_format
    request.format = :turbo_stream
  end

  def set_pending_participant
    @pending_participant = PendingParticipant.find(params[:id])
  end

  def set_site
    @site = Site.find(params[:site_id])
  end

  def set_info
    @tab = "participants"
    @topbar_tab = "pending_participants"
  end

  def load_document_types
    @document_types = if @pending_participant.role_context
                        RoleDocumentTypesQuery.new(@pending_participant.role_context).call
                      else
                        []
                      end
  end

  def pending_participant_params
    params_with_formatted_date = params.require(:pending_participant).permit(
      :first_name,
      :last_name,
      :birth_date,
      :is_interim,
      :company_id,
      :assignment_id,
      :participant_role_site_id,
      :is_valid,
      :valid_from,
      :valid_until,
      :photo
    )

    # Conversion de la date du format français (dd/mm/yyyy) vers le format ISO (yyyy-mm-dd)
    if params_with_formatted_date[:birth_date].present? && params_with_formatted_date[:birth_date].match?(%r{\d{2}/\d{2}/\d{4}})
      date_parts = params_with_formatted_date[:birth_date].split('/')
      params_with_formatted_date[:birth_date] = "#{date_parts[2]}-#{date_parts[1]}-#{date_parts[0]}"
    end

    params_with_formatted_date
  end

  def sync_assignment_with_company
    # Skip si pas de params ou si le role_site_id n'est pas présent
    return if params[:pending_participant].blank? ||
              params[:pending_participant][:participant_role_site_id].blank? ||
              params[:pending_participant][:company_id].blank?

    # Récupérer le role_site et vérifier si l'attribut same_company est true
    role_site_id = params[:pending_participant][:participant_role_site_id]
    role_site = @site.participant_role_sites.find_by(id: role_site_id)

    return unless role_site&.same_company

    # Si l'attribut est true, rechercher l'assignment correspondant à la company
    company_id = params[:pending_participant][:company_id]
    matching_assignment = @site.assignments.find_by(company_id: company_id)

    # Si un assignment existe, l'utiliser
    return unless matching_assignment

    params[:pending_participant][:assignment_id] = matching_assignment.id
  end

  def update_validity_status_if_needed(original_values)
    # Vérifier si l'un des attributs de validité a changé
    validity_changed = VALIDITY_ATTRS.any? do |attr|
      original_values[attr].to_s != @pending_participant.send(attr).to_s
    end

    # Si aucun attribut n'a changé, ne pas mettre à jour
    return unless validity_changed

    # Réinitialiser le role_context mis en cache avant la mise à jour
    if @pending_participant.instance_variable_defined?(:@role_context)
      @pending_participant.remove_instance_variable(:@role_context)
    end

    @pending_participant.update_validity_status
  end
end
