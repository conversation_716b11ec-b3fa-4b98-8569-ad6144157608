class ConditionGroupsController < ApplicationController
  before_action :set_condition_group, only: %i[show edit update destroy manage]
  before_action :set_page_infos
  before_action :set_site

  def new
    @condition_group = ConditionGroup.new
    authorize @condition_group
    render layout: request.format.turbo_stream? ? 'modal' : 'application'
  end

  def create
    @condition_group = ConditionGroup.new(condition_group_params)
    authorize @condition_group

    if @condition_group.save
      respond_to do |format|
        format.turbo_stream
        format.html { redirect_to conditions_path, notice: "Groupe de conditions créé avec succès" }
      end
    else
      respond_to do |format|
        format.turbo_stream { render :new, status: :unprocessable_entity }
        format.html { render :new, status: :unprocessable_entity }
      end
    end
  end

  def edit
    authorize @condition_group
    render layout: request.format.turbo_stream? ? 'modal' : 'application'
  end

  def update
    authorize @condition_group

    if @condition_group.update(condition_group_params)
      respond_to do |format|
        format.turbo_stream
        format.html { redirect_to conditions_path, notice: "Groupe de conditions mis à jour avec succès" }
      end
    else
      respond_to do |format|
        format.turbo_stream { render :edit, status: :unprocessable_entity }
        format.html { render :edit, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    authorize @condition_group
    @condition_group.destroy
    redirect_to site_conditions_path(@site), notice: "Groupe de conditions supprimé avec succès"
  end

  def manage
    authorize @condition_group
    @elements_in_group = @condition_group.condition_group_mappings.includes(:element).map(&:element)

    condition_ids_in_group = @elements_in_group.select { |e| e.is_a?(Condition) }.map(&:id)
    @available_conditions = Condition
                            .where(company_id: @site.company_id)
                            .or(Condition.where(company_id: nil))
                            .where.not(id: condition_ids_in_group)
                            .includes(:company)

    logical_node_ids_in_group = @elements_in_group.select { |e| e.is_a?(LogicalNode) }.map(&:id)
    @available_logical_nodes = LogicalNode
                               .where.not(id: logical_node_ids_in_group)

    render layout: request.format.turbo_stream? ? 'modal' : 'application'
  end

  private

  def set_condition_group
    @condition_group = ConditionGroup.find(params[:id])
  end

  def set_site
    @site = Site.find(params[:site_id])
  end

  def set_page_infos
    @meta_title = "Gestion des groupes de conditions"
    @page_title = "Groupes de conditions"
    @tab = "settings"
  end

  def condition_group_params
    params.require(:condition_group).permit(
      :humanized_name,
      :short_name,
      :name,
      :target_type,
      :purpose,
      :is_mandatory,
      :entity_type,
      :context_type,
      :global,
      :company_id
    )
  end
end
