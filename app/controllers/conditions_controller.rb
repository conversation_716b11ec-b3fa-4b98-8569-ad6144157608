class ConditionsController < ApplicationController
  before_action :set_page_infos
  before_action :set_site

  def index
    @conditions = policy_scope(Condition).includes(:company)
    @condition_groups = policy_scope(ConditionGroup).includes(:company)
    @logical_nodes = LogicalNode.all

    # Statistiques
    @total_conditions = @conditions.count
    @total_groups = @condition_groups.count
    @conditions_in_groups = ConditionGroupMapping.count
    @global_conditions = @conditions.where(global: true).count
    @topbar_tab = "conditions"
  end

  def new
    @condition = Condition.new
    authorize @condition
    render layout: request.format.turbo_stream? ? 'modal' : 'application'
  end

  def create
    @condition = Condition.new(condition_params.except(:parameters))
    @condition.company = @site.company

    # Créer les paramètres polymorphiques selon le type de condition
    create_parameters_for_condition

    authorize @condition

    if @condition.save
      @conditions = policy_scope(Condition).includes(:company)
      respond_to do |format|
        format.html { redirect_to site_conditions_path(@site), notice: "Condition créée avec succès" }
        format.turbo_stream
      end
    else
      respond_to do |format|
        format.html { render :new, status: :unprocessable_entity }
        format.turbo_stream { render :new, layout: 'modal', status: :unprocessable_entity }
      end
    end
  end

  def edit
    @condition = Condition.find(params[:id])
    authorize @condition
    render layout: request.format.turbo_stream? ? 'modal' : 'application'
  end

  def update
    @condition = Condition.find(params[:id])
    authorize @condition

    @condition.assign_attributes(condition_params.except(:parameters))

    create_parameters_for_condition

    if @condition.save # Sauvegarde la condition et gère les paramètres associés (nouveau/ancien).
      respond_to do |format|
        @conditions = policy_scope(Condition).includes(:company)
        format.html { redirect_to site_conditions_path(@site), notice: "Condition mise à jour avec succès." }
        format.turbo_stream
      end
    else
      respond_to do |format|
        format.html { render :edit, status: :unprocessable_entity }
        format.turbo_stream { render :edit, layout: 'modal', status: :unprocessable_entity }
      end
    end
  end

  def destroy
    @condition = Condition.find(params[:id])
    @condition.destroy
    authorize @condition
    @conditions = policy_scope(Condition).includes(:company)
    respond_to do |format|
      format.turbo_stream
    end
  end

  private

  def set_page_infos
    @meta_title = "Gestion des conditions"
    @page_title = "Conditions"
    @tab = "settings"
  end

  def set_site
    @site = Site.find(params[:site_id])
  end

  def create_parameters_for_condition
    # Récupération des paramètres soumis par le formulaire
    params_attrs = params[:condition][:parameters] || {}

    # Détermination du type de paramètre à créer en fonction du type de condition
    condition_type = params[:condition][:condition_type]
    parameter_type = case condition_type
                     when 'presence'
                       'presence_parameter'
                     when 'date_min'
                       'date_min_parameter'
                     when 'date_max'
                       'date_max_parameter'
                     when 'should_return_true'
                       'should_return_true_parameter'
                     when 'should_return_false'
                       'should_return_false_parameter'
                     when 'field_equals'
                       'field_equals_parameter'
                     when 'custom'
                       'custom_parameter'
                     else
                       raise ArgumentError, "Type de condition inconnu: #{condition_type}"
                     end

    # Préparation des attributs pour le paramètre
    attrs = {}

    # Conversion des IDs en objets pour les relations belongs_to
    attrs[:document_type] = DocumentType.find(params_attrs[:document_type]) if params_attrs[:document_type].present?

    # Gestion du document_field (qui peut être un ID ou une chaîne)
    if params_attrs[:document_field].present?
      if params_attrs[:document_field].match?(/^\d+$/) # Si c'est un nombre
        attrs[:document_field] = DocumentField.find(params_attrs[:document_field])
      else
        # Si c'est une chaîne, cherchons le document_field par son nom dans le document_type
        document_type = attrs[:document_type]
        raise ArgumentError, "Impossible de trouver ou créer le champ sans type de document" unless document_type

        field = document_type.document_fields.find_by(name: params_attrs[:document_field]) ||
                document_type.document_fields.find_by(name_humanized: params_attrs[:document_field])

        if field
          attrs[:document_field] = field
        else
          # Si le champ n'existe pas, créons-le
          attrs[:document_field] = DocumentField.create!(
            name: params_attrs[:document_field],
            name_humanized: params_attrs[:document_field],
            document_type: document_type,
            field_type: 'date' # Type par défaut, à adapter selon le contexte
          )
        end

      end
    end

    # Ajout d'autres attributs spécifiques
    attrs[:expected_value] = params_attrs[:expected_value] if params_attrs[:expected_value].present?
    attrs[:custom_method_name] = params_attrs[:custom_method_name] if params_attrs[:custom_method_name].present?
    attrs[:catalog_name] = params_attrs[:catalog_name] if params_attrs[:catalog_name].present?
    attrs[:source_relation] = params_attrs[:source_relation] if params_attrs[:source_relation].present?

    # Création du paramètre via la factory
    @condition.parameters = Conditions::ParameterFactory.create(parameter_type, attrs)
  end

  def condition_params
    params.require(:condition).permit(
      :name,
      :description,
      :fallback_message,
      :condition_type,
      :target_type,
      :purpose,
      :global,
      parameters: %i[document_type document_field expected_value custom_method_name catalog_name
                     source_relation]
    )
  end
end
