class UploadsController < ApplicationController
  include TurboUpdatable
  before_action :set_site
  before_action :set_infos

  def index
    @topbar_tab = params[:status] || "uploads"
    set_uploads
  end

  def show
    @upload = Upload.find(params[:id])
    @scope = params[:scope]
    @status = params[:status]
    @documents = @upload.documents.includes(:file_attachment, :document_type, extracted_fields: :document_field)
    respond_to do |format|
      format.html
      format.turbo_stream { render layout: "sidebar" }
    end
  end

  def create
    site = Site.find(params[:site_id])
    files = params[:files].values.map { |file_params| file_params[:file] }

    creation_service = Uploads::CreationService.new(files, current_user, :file_upload, site)
    document_types = DocumentType.for_site(site).to_a

    result = creation_service.create(document_types: document_types)

    if result[:success]
      redirect_to site_uploads_path(site), notice: "Fichiers importés avec succès"
    else
      redirect_to site_uploads_path(site), alert: result[:errors].values.flatten.join(", ")
    end
  end

  def destroy
    service_result = Uploads::UploadsDestroyerService.new(request:, user: current_user).call
    global_renderer = service_result.global_renderer

    respond_to do |format|
      format.turbo_stream { render turbo_stream: global_renderer.render_turbo_stream(view_context) }
    end
    # if params[:id].present?
    #   @uploads = Upload.find(params[:id])
    # elsif params[:ids_list].present? && params[:ids_list].is_a?(Array)
    #   @uploads = Upload.where(id: params[:ids_list])
    # else
    #   return render_turbo_response(false, { error_message: "Aucun fichier valide spécifié pour la suppression." })
    # end
    # service_result = Documents::Destroyer::DocumentDestroyerService.call(
    #   destroyable: @uploads,
    #   request: request,
    #   user: current_user,
    #   site: @site
    # )
    # render_turbo_response(service_result.success?, service_result.options)
    # success = false
    # if params[:id].present?
    #   @upload = Upload.find(params[:id])
    #   @upload.destroy
    #   @uploads_to_remove = [@upload] # Stocker pour la vue
    #   success = true
    # elsif params[:ids_list].present? && params[:ids_list].is_a?(Array) # Vérifier que c'est un tableau
    #   # Rails devrait automatiquement parser 'ids_list[]' en tableau
    #   @uploads_list = Upload.where(id: params[:ids_list])
    #   @uploads_to_remove = @uploads_list.to_a # Charger avant de détruire pour la vue
    #   @uploads_list.destroy_all
    #   success = true
    # else
    #   # Gérer les cas d'erreur: pas d'ID ou format incorrect de ids_list
    #   respond_to do |format|
    #     format.turbo_stream do
    #       render turbo_stream: turbo_stream.prepend("flash", partial: "shared/flash", locals: { alert: "Aucun fichier valide spécifié pour la suppression." }),
    #              status: :unprocessable_entity
    #     end
    #     format.html do
    #       redirect_to site_uploads_path(@site), alert: "Aucun fichier valide spécifié pour la suppression."
    #     end
    #   end
    #   return # Important pour éviter double render/redirect
    # end
    # respond_to do |format|
    #   if success
    #     @uploads = set_uploads
    #     partials = [turbo_stream.append("flashes-container", partial: "shared/flashes", locals: { notice: "Importation supprimée avec succès." }),
    #                 turbo_stream.update("uploads-index-datatable-container", partial: "uploads/index", locals: { uploads: @uploads }),
    #                 turbo_stream.replace("completed-uploads-count", partial: "uploads/completed_uploads_count", locals: { count: @uploads.count }),
    #                 turbo_stream.replace("pending-uploads-count", partial: "uploads/pending_uploads_count", locals: { count: @uploads.where(status: :pending).count }),
    #                 turbo_stream.replace("error-uploads-count", partial: "uploads/error_uploads_count", locals: { count: @uploads.where(status: :error).count })]
    #     format.turbo_stream do
    #       render turbo_stream: partials
    #     end
    #     format.html do
    #       redirect_to site_uploads_path(@site), notice: "Importation supprimée avec succès."
    #     end
    #   else
    #     format.turbo_stream do
    #       render turbo_stream: turbo_stream.append("flashes-container", partial: "shared/flashes", locals: { alert: "Impossible de supprimer cette importation." }),
    #              status: :unprocessable_entity
    #     end
    #     format.html { redirect_to site_uploads_path(@site), alert: "Impossible de supprimer cette importation." }
    #   end
    # end
  end

  def update
    @upload = Upload.find(params[:id])
    @upload.update(upload_params)
    redirect_to site_uploads_path(@site), notice: "Document mis à jour avec succès."
  end

  def bulk_validate
    # S'assurer que les IDs sont présents et sont un tableau
    ids = params[:ids_list]
    unless ids.is_a?(Array) && ids.present?
      respond_to do |format|
        format.turbo_stream do
          render turbo_stream: turbo_stream.prepend("flash", partial: "shared/flash", locals: { alert: "Aucun document sélectionné." }),
                 status: :unprocessable_entity
        end
        format.html { redirect_to site_uploads_path(@site), alert: "Aucun document sélectionné." }
      end
      return
    end

    # Trouver les uploads correspondants DANS le site actuel
    @uploads_to_update = @site.uploads.where(id: ids)

    # Mettre à jour efficacement le statut
    updated_count = @uploads_to_update.update_all(status: :validated, updated_at: Time.current)

    # Recharger les instances pour la vue Turbo Stream (car update_all ne les met pas à jour)
    @uploads_to_update = @site.uploads.where(id: ids)

    respond_to do |format|
      format.turbo_stream
      format.html do
        redirect_to site_uploads_path(@site), notice: "#{updated_count} document(s) marqué(s) comme validé(s)."
      end
    end
  end

  private

  def upload_params
    params.require(:upload).permit(:status)
  end

  def set_site
    @site = Site.find(params[:site_id])
  end

  def set_infos
    @tab = "uploads"
    @topbar_tab = "uploads"
  end

  def set_uploads
    scope = case params[:scope]
            when "company" then current_user.company
            when "all" then :all
            else current_user
            end
    @uploads = Perimeters::UserPerimeter
               .new(site: @site, user: current_user)
               .uploads_scope(
                 status: @topbar_tab == "validated" ? :validated : %i[pending processing failed completed],
                 scope: scope,
                 include_documents: true
               ).order(created_at: :desc)
  end
end
