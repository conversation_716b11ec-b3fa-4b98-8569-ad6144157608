class ParticipantsController < ApplicationController
  include PhotoAttachable
  include TurboUpdatable

  before_action :set_page_infos
  before_action :set_participant, only: %i[show update_image edit update sync_with_external_service]
  before_action :permissions

  def index
    category = params[:category] || "partner"
    @participants = policy_scope(Participant)
                    .with_all_site_data(@site.id)
                    .includes(:vetting_process)
                    .where(assignment: { assignment_role_site: AssignmentRoleSite.where(site: @site,
                                                                                        assignment_role: AssignmentRole.where(assignment_role_category: AssignmentRoleCategory.find_by(code: category))) })
    if category == "interim" && @current_user_site.role?(:interim)
      @participants = policy_scope(Participant)
                      .with_all_site_data(@site.id)
                      .where(person: { company: @current_user_company })
    end

    # @overloaded_qualifications_ids = @site.overloaded_qualifications_ids

    @failed_records_by_group = FailedConditionRecord
                               .joins(:group_result)
                               .where(group_result: { subject: @participants })
                               .order(failed_at: :desc)
                               .group_by(&:group_result_id)

    determine_topbar_tab
  end

  def show
    @documents = @participant.documents
    @role_context = @participant.role_context
    @document_types_config = RoleDocumentTypesQuery.new(@role_context).call
    @needed_document_types = @document_types_config.map { |config| config[:document_type] }.uniq.compact
    @validation_status = DocumentValidationStatusService.new(@participant).call
    @topbar_tab = "access"
    @page_title = @participant.full_name
    @tab = "participant"
  end

  def new
    @participant = Participant.new
    @participant.build_person
    @topbar_tab = "new_participant"
    @photo_form = params[:photo_form] || true
    @assignments = Perimeters::UserPerimeter.new(user: current_user, site: @site).assignments_scope
    @employers = Perimeters::UserPerimeter.new(user: current_user, site: @site).companies_scope
    if params[:encoded_partials_config].present?
      partials_config = decode_partials_config(params)
      document = Document.find(params[:document_id])
      return render_turbo_response(
        true,
        partials_config: partials_config,
        context: { participant: @participant, site: @site, assignments: @assignments, employers: @employers,  document: }
      )
    end
  end

  def create
    # 1. Construire le participant avec les paramètres
    @participant = Participant.new(participant_params)

    # 2. Gérer l'assignation automatique si elle est vide
    if @participant.assignment_id.blank?
      employer_id = @participant.person&.company_id # Utilise dig pour sécurité si person peut être nil

      if employer_id.present?
        # Chercher les assignments de l'employeur sur ce site
        possible_assignments = Assignment.where(company_id: employer_id, site_id: @site.id)

        if possible_assignments.count == 1
          # Si une seule correspond, l'assigner automatiquement
          @participant.assignment = possible_assignments.first
        else
          # Si 0 ou plusieurs, impossible de choisir automatiquement -> Erreur
          # L'erreur sera ajoutée par la validation standard `belongs_to :assignment` lors du save
          # On peut préventivement ajouter un message plus clair ici si on veut.
          # @participant.errors.add(:assignment_id, :ambiguous_or_missing_for_employer, message: "Sélectionnez une entreprise intervenante (aucune affectation unique trouvée pour l'employeur sur ce site).")
        end
      else
        # Si pas d'employeur fourni non plus, on ne peut rien faire
        # L'erreur sera ajoutée par la validation standard `belongs_to :assignment`
        # @participant.errors.add(:assignment_id, :blank_without_employer, message: "Sélectionnez une entreprise intervenante ou un employeur.")
      end
    end

    # 3. Autoriser l'action
    authorize @participant

    # 4. Tenter de sauvegarder
    if @participant.save
      # Les callbacks et validations (y compris validate_company_assignment_correspondence) s'exécutent

      # Traiter la photo si elle a été soumise
      attach_photo(@participant, params, :participant)

      redirect_to site_participant_path(@site, @participant), notice: 'Participant créé avec succès.'
      # Optionnel: Réponse Turbo Stream
      # ... (code turbo stream comme avant) ...
    else
      # Si la sauvegarde échoue (y compris si assignment_id est resté vide ou invalide)
      @participant.build_person unless @participant.person # Pour réafficher le form
      respond_to do |format|
        format.html { render :new, status: :unprocessable_entity }
        format.turbo_stream do
          render turbo_stream: turbo_stream.update("modal-container") {
            render layout: "layouts/modal" do
              # Le formulaire affichera les erreurs de @participant (ex: "Assignment doit exister")
              render "form"
            end
          }, status: :unprocessable_entity
        end
      end
    end
  end

  def search
    query = "%#{params[:query].downcase}%"
    @participants = Participant
                    .joins(:person, assignment: :site)
                    .where(assignment: { site: @site })
                    .where("LOWER(people.first_name) LIKE ? OR LOWER(people.last_name) LIKE ?", query, query)
                    .limit(10)

    render json: @participants.map { |p|
      { id: p.id, full_name: p.full_name, assignment_company_name: p.assignment_company_name }
    }
  end

  def edit
    @current_participant_role_site = @participant.participant_role_site
  end

  def update
    # Traitement spécial pour employer_id
    if participant_params[:employer_id].present?
      @participant.person.update(company_id: participant_params[:employer_id])
    end

    original_role_site_id = @participant.participant_role_site_id

    # Mise à jour des autres paramètres
    if @participant.update(participant_params.except(:employer_id))
      # Si le rôle a changé, mettre à jour les groupes de conditions et la validité
      if original_role_site_id != @participant.participant_role_site_id
        # Effacer les valeurs mémorisées pour forcer le recalcul
        @participant.instance_variable_set(:@site_role_condition_groups, nil)
        @participant.instance_variable_set(:@condition_groups, nil)
        # Mettre à jour la validité
        @participant.update_validity_status
      end

      @participant.reload
      redirect_to site_participant_path(@site, @participant)
    else
      render :edit
    end
  end

  # Mise à jour d'image via AJAX
  def update_image
    result, status = handle_image_update(@participant, params, :participant)
    render json: result, status: status
  end

  def sync_with_external_service
    authorize @participant

    result = ExternalApis::SynchronizationService.new(
      type: :user,
      participant_id: @participant.id,
      site_id: @site.id
    ).call

    if result[:success]
      data = result[:data]
      service_name = data['service']&.capitalize || 'service externe'
      action = data['action'] == 'created' ? 'créé' : 'mis à jour'

      redirect_to site_participant_path(@site, @participant),
                  notice: "Participant #{action} avec succès dans #{service_name}."
    else
      redirect_to site_participant_path(@site, @participant),
                  alert: "Échec de la synchronisation: #{result[:error]}"
    end
  rescue StandardError => e
    redirect_to site_participant_path(@site, @participant),
                alert: "Erreur: #{e.message}"
  end

  def destroy
    @participants = Participant.where(id: params[:ids_list] || params[:id])
    authorize @participants, :destroy?
    @participants.destroy_all
    flash[:notice] = "Participant supprimé avec succès"
    respond_to do |format|
      format.json { render json: { redirect_url: site_participants_path(@site) } }
      format.html { redirect_to site_participants_path(@site) }
    end
  end

  private

  def participant_params
    params.require(:participant).permit(
      :participant_role_site_id,
      :employer_id,
      :assignment_id,
      :photo,
      person_attributes: %i[id first_name last_name birth_date company_id]
    )
  end

  def set_participant
    @participant = Participant.find(params[:id])
  end

  def set_page_infos
    @meta_title = "#{@site.name} - Intervenants"
    @page_title = "Intervenants"
    @tab = "participants"
  end

  def determine_topbar_tab
    @topbar_tab = params[:category]
  end

  def permissions
    authorize Participant
  end
end
