module DocumentableConcern
  extend ActiveSupport::Concern

  included do
    before_action :set_document
  end

  def set_documentable
    @documentable = find_documentable_from_params
    instance_variable_set("@#{params[:model]}", @documentable)
  end

  def set_documentable_documents
    return if @documentable.blank?

    @documents = @documentable.documents
    @validation_status = DocumentValidationStatusService.new(@documentable).call
  end

  def set_documentable_validation_status
    if @document.documentable.present?
      @validation_status = DocumentValidationStatusService.new(@document.documentable).call
      @document_validation_status = @validation_status[:documents].find do |doc_status|
        doc_status[:document].id == @document.id
      end
    end
  end

  def find_documentable_from_params
    unless params[:model].present?
      raise ArgumentError,
            "Model invalide: #{params[:model]}. Rajouter dans route.rb => model: 'nom_du_model'"
    end

    model_key = params[:model].downcase
    model_class = ALLOWED_DOCUMENTABLE_MODELS[model_key]

    raise ArgumentError, "Model non autorisé: #{params[:model]}." unless model_class

    documentable = model_class.find_by(id: params[:model_id] || params[:id])
    raise ActiveRecord::RecordNotFound, "#{model_class} #{params[:id]} non trouvé" unless documentable

    documentable
  end

  # Liste des modèles autorisés comme documentables
  ALLOWED_DOCUMENTABLE_MODELS = {
    'participant' => Participant,
    'pendingparticipant' => PendingParticipant,
    'assignment' => Assignment,
    'companyperson' => CompanyPerson,
    'companyconnection' => CompanyConnection,
    'company' => Company,
    'person' => Person
  }.freeze

  private

  def set_document
    @document = Document.find(params[:id])
  end
end
