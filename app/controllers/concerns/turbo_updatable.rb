module TurboUpdatable
  extend ActiveSupport::Concern
  include ApplicationHelper

  # Décode la configuration des partiels depuis les paramètres avec vérification de sécurité
  # @param params [ActionController::Parameters] Les paramètres de la requête
  # @param max_age [Integer] Durée maximale de validité en secondes (défaut: 1 heure)
  # @return [Array<Hash>] Configuration des partiels décodée et vérifiée
  def decode_partials_config(params, max_age = 3600)
    if params[:encoded_partials_config].present?
      # Décode la configuration encodée en Base64 avec vérification de signature
      decode_json_param(params[:encoded_partials_config], max_age)
    elsif params[:partials_config].present?
      # Format JSON déjà parsé par Rails - à utiliser uniquement en développement ou pour des tests
      unless Rails.env.development? || Rails.env.test?
        Rails.logger.warn("Utilisation de partials_config non sécurisé en environnement #{Rails.env}")
      end
      params[:partials_config]
    else
      []
    end
  end

  # Mise à jour des partiels via Turbo Stream en fonction des paramètres
  # @param partials_config [Array<Hash>] Configuration des partiels à mettre à jour
  #   Chaque élément doit contenir:
  #   - id: ID de l'élément à remplacer
  #   - partial: Chemin du partial à utiliser pour le remplacement
  # @param context [Hash] Contexte pour générer les variables locales
  # @return [Array<Turbo::StreamActions::ReplaceAction>] Les actions Turbo Stream à rendre
  def update_partials_via_turbo(partials_config, context = {})
    return [] if partials_config.blank?

    # Créer les streams pour chaque partial
    partials_config.map do |config|
      partial_id = config[:id]
      partial_path = config[:partial]
      turbo_stream.update(
        partial_id,
        partial: partial_path,
        locals: PartialLocalsRegistry.generate_locals(partial_path, partial_id, context)
      )
    end
  end

  # Affiche un message flash via Turbo Stream
  # @param message [String] Le message à afficher
  # @param type [Symbol] Le type de message (:notice, :alert, etc.)
  # @param action [Symbol] L'action à effectuer (:replace ou :append)
  # @return [Turbo::StreamActions::ReplaceAction] L'action Turbo Stream pour le flash
  def flash_turbo_stream(message, type = :notice, action = :replace)
    if action == :append
      turbo_stream.append("flashes-container",
                          partial: "shared/flashes",
                          locals: { type.to_sym => message })
    else
      turbo_stream.replace("flashes-container",
                           partial: "shared/flashes",
                           locals: { type.to_sym => message })
    end
  end

  # Gère la réponse après une action (création, mise à jour, suppression)
  # @param success [Boolean] Indique si l'action a réussi
  # @param options [Hash] Options de configuration
  #   - partials_config: [Array<Hash>] Configuration des partiels à mettre à jour
  #   - context: [Hash] Contexte pour générer les variables locales
  #   - success_message: [String] Message à afficher en cas de succès
  #   - error_message: [String] Message à afficher en cas d'erreur
  #   - success_redirect: [String|Symbol|Array] Redirection en cas de succès (pour format HTML)
  #   - error_redirect: [String|Symbol|Array] Redirection en cas d'erreur (pour format HTML)
  def render_turbo_response(success, options = {})
    options = {
      partials_config: [],
      remove_items: [],
      context: {},
      success_message: nil,
      error_message: nil,
      success_redirect: nil,
      error_redirect: nil,
      close_modal: false,
      close_sidebar: false,
      force_redirect_to: nil
    }.merge(options)

    if options[:force_redirect_to].present?
      return redirect_to options[:force_redirect_to], notice: options[:success_message] if success

      return redirect_to options[:force_redirect_to], alert: options[:error_message]
    end

    respond_to do |format|
      # Réponse au format Turbo Stream
      format.turbo_stream do
        if success
          # Supprimer les items (array de string)
          streams = []
          if options[:remove_items].present?
            options[:remove_items].each do |item|
              streams << turbo_stream.remove(item)
            end
          end
          streams.concat(update_partials_via_turbo(options[:partials_config], options[:context]))
          if options[:success_message].present?
            streams << flash_turbo_stream(options[:success_message], :notice,
                                          :append)
          end
          # Fermer la modal et la sidebar
          streams << turbo_stream.update("modal-container", "") if options[:close_modal]
          streams << turbo_stream.update("sidebar-container", "") if options[:close_sidebar]
          render turbo_stream: streams
        elsif options[:error_message].present?
          render turbo_stream: flash_turbo_stream(options[:error_message], :alert,
                                                  :append)
        end
      end

      # Réponse au format HTML
      format.html do
        if success
          redirect_options = { notice: options[:success_message] }
          redirect_to options[:success_redirect] || root_path, redirect_options
        else
          redirect_options = { alert: options[:error_message] }
          redirect_to options[:error_redirect] || :back, redirect_options
        end
      end
    end
  end
end
