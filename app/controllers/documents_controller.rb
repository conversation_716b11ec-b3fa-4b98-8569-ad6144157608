class DocumentsController < ApplicationController
  include FileServing
  include TurboUpdatable
  include FeatureHelper
  include DocumentableConcern

  before_action :set_document, only: %i[show serve destroy edit documentable_details update_document_scope update_document_type update_extracted_fields update_documentable]
  before_action :set_site_for_updates, only: %i[documentable_details update_document_scope update_document_type update_extracted_fields update_documentable]

  def index
    authorize Document
    @site = Site.find(params[:site_id])
    @meta_title = "#{@site.name} - Documents"
    @page_title = "Documents"
    @topbar_tab = "documents"
    @tab = params[:model]
    set_documentable
    set_documentable_documents
  end

  def serve
    serve_file_resource(@document)
  end

  def edit
    @site = Site.find_by(id: params[:site_id]) || @document.site || @document.documentable.site
    @scope = params[:scope] if params[:scope].present?
    @status = params[:status] if params[:status].present?
    set_documentable_validation_status
    render layout: request.format.turbo_stream? ? 'modal' : 'application'
  end

  def create_batch
    authorize Document, :create_batch?
    @site = Site.find(params[:site_id])

    # Déléguer le traitement au service
    service = Documents::BatchCreationService.new(@site, current_user, params)
    result = service.process

    flashes_type = result[:error_count].zero? ? :alert : :notice
    respond_to do |format|
      format.turbo_stream do
        redirect_to request.referer, flashes_type => result[:message]
      end
    end
  end

  def destroy
    service_result = Documents::DocumentDestroyerService.new(request:, user: current_user).call
    global_renderer = service_result.global_renderer

    respond_to do |format|
      format.turbo_stream { render turbo_stream: global_renderer.render_turbo_stream(view_context) }
    end
  end

  def document_history
    authorize Document, :document_history?
    @document = Document.find(params[:id])
    history_service = Documents::ExtractedFieldsHistoryService.new(@document)
    history_data = history_service.call.select(&:displayed)
    @history = history_data.group_by(&:field_name)
    @validation_status = DocumentValidationStatusService.new(@document.documentable).call
    @document_validation_status = @validation_status[:documents].find do |doc_status|
      doc_status[:document].id == @document.id
    end
    render layout: request.format.turbo_stream? ? 'modal' : 'application'
  end

  def update_document_scope
    service_result = Documents::Updater::DocumentScopeUpdaterService.new(request:, user: current_user).call
    global_renderer = service_result.global_renderer

    respond_to do |format|
      format.turbo_stream { render turbo_stream: global_renderer.render_turbo_stream(view_context) }
    end
  end

  def update_document_type
    service_result = Documents::Updater::DocumentTypeUpdaterService.new(request:, user: current_user).call
    global_renderer = service_result.global_renderer

    respond_to do |format|
      format.turbo_stream { render turbo_stream: global_renderer.render_turbo_stream(view_context) }
    end
  end

  def update_extracted_fields
    service_result = Documents::Updater::ExtractedFieldsUpdaterService.new(request:, user: current_user).call
    global_renderer = service_result.global_renderer

    respond_to do |format|
      format.turbo_stream { render turbo_stream: global_renderer.render_turbo_stream(view_context) }
    end
  end

  def update_documentable
    service_result = Documents::Updater::DocumentableUpdaterService.new(request:, user: current_user).call
    global_renderer = service_result.global_renderer

    respond_to do |format|
      format.turbo_stream { render turbo_stream: global_renderer.render_turbo_stream(view_context) }
    end
  end

  private

  def set_document
    @document = Document.find(params[:id])
  end

  def set_site_for_updates
    @site = Site.find_by(id: params[:site_id]) || @document&.site || @document&.documentable&.site
    raise ActiveRecord::RecordNotFound, "Site not found for document update" if @site.nil? && (%w[update_document_scope update_document_type update_extracted_fields update_documentable].include?(action_name))
  end
end
