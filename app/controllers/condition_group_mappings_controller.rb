class ConditionGroupMappingsController < ApplicationController
  before_action :set_condition_group
  before_action :set_element, only: [:create]
  before_action :set_mapping, only: [:destroy]
  before_action :set_site

  def new
    authorize @condition_group
    @conditions_in_group = @condition_group.conditions
    @available_conditions = policy_scope(Condition).includes(:company) - @conditions_in_group

    render layout: request.format.turbo_stream? ? 'modal' : 'application'
  end

  def create
    authorize @condition_group, :update?

    unless @element
      flash[:alert] = "L'élément à ajouter n'a pas été trouvé ou n'est pas autorisé."
      prepare_available_and_assigned_elements
      respond_to_error("Impossible d'ajouter l'élément.")
      return
    end

    @mapping = @condition_group.condition_group_mappings.build(element: @element)

    if @mapping.save
      flash[:notice] = "Élément ajouté au groupe."
      prepare_available_and_assigned_elements
      respond_to_success
    else
      flash[:alert] = "Erreur lors de l'ajout de l'élément au groupe: #{@mapping.errors.full_messages.join(', ')}"
      prepare_available_and_assigned_elements
      respond_to_error("Impossible d'ajouter l'élément.", :unprocessable_entity)
    end
  end

  def destroy
    authorize @condition_group, :update?

    if @mapping&.destroy
      flash[:notice] = "Élément retiré du groupe."
    else
      flash[:alert] = "Impossible de retirer l'élément demandé."
    end
    prepare_available_and_assigned_elements
    respond_to_success
  end

  private

  def set_condition_group
    @condition_group = ConditionGroup.find_by_id(params[:condition_group_id])
    handle_resource_not_found(@condition_group, "Groupe de conditions non trouvé.")
  end

  def set_element
    element_type_param = params[:element_type]
    element_id_param = params[:element_id]

    if element_type_param.blank? || element_id_param.blank?
      @element = nil
      return
    end

    klass = element_type_param.safe_constantize
    if klass && [Condition, LogicalNode].include?(klass)
      @element = klass.find_by_id(element_id_param)
    else
      @element = nil
    end
  end

  def set_mapping
    @mapping = ConditionGroupMapping.find_by_id(params[:id])
    @mapping = nil if @mapping && @mapping.condition_group_id != @condition_group&.id
    handle_resource_not_found(@mapping, "Mapping non trouvé ou non autorisé.")
  end

  def set_site
    @site = Site.find_by_id(params[:site_id])
    handle_resource_not_found(@site, "Site non trouvé.")
  end

  def prepare_available_and_assigned_elements
    return unless @condition_group && @site

    @elements_in_group = @condition_group.condition_group_mappings.includes(:element).map(&:element)

    condition_ids_in_group = @elements_in_group.select { |e| e.is_a?(Condition) }.map(&:id)
    @available_conditions = Condition
                            .where(company_id: @site.company_id)
                            .or(Condition.where(company_id: nil))
                            .where.not(id: condition_ids_in_group)
                            .includes(:company)

    logical_node_ids_in_group = @elements_in_group.select { |e| e.is_a?(LogicalNode) }.map(&:id)
    @available_logical_nodes = LogicalNode
                               .where.not(id: logical_node_ids_in_group)
  end

  def handle_resource_not_found(resource, message)
    return if resource.present? || response_body

    flash[:alert] = message
    redirect_to site_conditions_path(params[:site_id] || @site) and return
  end

  def respond_to_success
    respond_to do |format|
      format.turbo_stream { render :update_lists_and_flashes }
      format.html { redirect_to manage_condition_group_path(@site, @condition_group) }
    end
  end

  def respond_to_error(default_message, status = :bad_request)
    flash[:alert] ||= default_message
    respond_to do |format|
      format.turbo_stream { render :update_lists_and_flashes, status: status }
      format.html { render 'condition_groups/manage', status: status }
    end
  end
end
