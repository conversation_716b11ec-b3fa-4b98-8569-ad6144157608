class DocumentablesController < ApplicationController
  include DocumentableConcern
  include <PERSON><PERSON><PERSON><PERSON>

  def new_participant
    @document = Document.find(params[:document_id])
    @site = Site.find_by(id: params[:site_id]) || @document&.site || @document&.documentable&.site
    # Préparer les variables nécessaires pour la création
    @assignments = Perimeters::UserPerimeter.new(user: current_user, site: @site).assignments_scope
    @employers = Perimeters::UserPerimeter.new(user: current_user, site: @site).companies_scope

    # Préparation complémentaire pour être compatible avec la méthode new de ParticipantsController
    @participant = Participant.new
    @participant.build_person

    # Similaire pour PendingParticipant (si feature activée)
    if feature_enabled?(:pending_participants, @site)
      @pending_participant = PendingParticipant.new(site: @site)
      @pending_participant.participant_role_site = @site.default_participant_role_site
    end

    render turbo_stream: turbo_stream.update(
      "documentable-card__content_participant_#{@document.id}",
      partial: "documents/participant_creation_form",
      locals: { document: @document, participant: @participant, pending_participant: @pending_participant }
    )
  end

  def documentable_details
    authorize @document, :documentable_details?

    render turbo_stream: turbo_stream.update(
      "documentable-details_#{@document.id}",
      partial: "documents/documentable_details",
      locals: { document: @document }
    )
  end
end
