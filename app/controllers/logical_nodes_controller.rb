# app/controllers/logical_nodes_controller.rb
class LogicalNodesController < ApplicationController
  before_action :set_site_if_present # Si vos logical_nodes sont scopés à un site
  before_action :set_logical_node, only: %i[show edit update destroy] # Assurez-vous que :show et :destroy sont pertinents
  before_action :load_form_dependencies, only: %i[new create edit update]

  def new
    @logical_node = LogicalNode.new
    render layout: request.format.turbo_stream? ? 'modal' : 'application'
  end

  def create
    @logical_node = LogicalNode.new(logical_node_params_processed)

    if @logical_node.save
      redirect_to site_conditions_path(@site), notice: 'Combinaison logique créée avec succès.'
    else
      redirect_to site_conditions_path(@site), alert: @logical_node.errors.full_messages.join('\n')
    end
  end

  def edit
    render layout: request.format.turbo_stream? ? 'modal' : 'application'
  end

  def update
    if @logical_node.update(logical_node_params_processed)
      redirect_to site_conditions_path(@site), notice: 'Combinaison logique mise à jour avec succès.'
    else
      redirect_to site_conditions_path(@site), alert: @logical_node.errors.full_messages.join('\n')
    end
  end

  # DELETE /sites/:site_id/logical_nodes/:id (ou /logical_nodes/:id)
  def destroy
    # @logical_node est chargé par set_logical_node
    @logical_node.destroy
    respond_to do |format|
      format.html { redirect_to logical_nodes_url_or_path, notice: 'Combinaison logique supprimée avec succès.' }
      format.turbo_stream { render turbo_stream: turbo_stream.remove(@logical_node) }
    end
  end

  private

  # Pour gérer le scoping par site, si applicable
  def set_site_if_present
    @site = Site.find(params[:site_id]) if params[:site_id].present?
  end

  def set_logical_node
    # Si scopé à un site :
    # @logical_node = @site ? @site.logical_nodes.find(params[:id]) : LogicalNode.find(params[:id])
    @logical_node = LogicalNode.find(params[:id]) # Version simple
  end

  def logical_node_params
    params.require(:logical_node).permit(:name, :fallback_message, :operator,
                                         :left_element_identifier, :right_element_identifier)
  end

  def logical_node_params_processed
    # Votre logique existante est bonne ici
    processed_params = logical_node_params.except(:left_element_identifier, :right_element_identifier)
    set_polymorphic_association(processed_params, :left_element, logical_node_params[:left_element_identifier])
    if logical_node_params[:operator] != 'not_op' && logical_node_params[:right_element_identifier].present?
      set_polymorphic_association(processed_params, :right_element, logical_node_params[:right_element_identifier])
    else
      processed_params[:right_element_type] = nil
      processed_params[:right_element_id] = nil
    end
    processed_params
  end

  def set_polymorphic_association(params_hash, association_name, identifier_value)
    # Votre logique existante est bonne ici
    return if identifier_value.blank?

    type, id = identifier_value.split('_')
    return unless type.present? && id.present? && ['Condition', 'LogicalNode'].include?(type)

    params_hash["#{association_name}_type"] = type
    params_hash["#{association_name}_id"] = id
  end

  def load_form_dependencies
    # Votre logique existante est bonne ici
    @conditions = Condition.order(:name)
    base_logical_nodes_scope = LogicalNode.order(:name)
    if @logical_node&.persisted? # S'applique à edit/update
      @logical_nodes_selectable = base_logical_nodes_scope.where.not(id: @logical_node.id)
    else # S'applique à new/create
      @logical_nodes_selectable = base_logical_nodes_scope.all
    end
  end

  # Helper pour générer les URLs en fonction du scoping par @site
  def logical_node_url_or_path(logical_node_instance)
    @site ? site_logical_node_path(@site, logical_node_instance) : logical_node_path(logical_node_instance)
  end

  def logical_nodes_url_or_path
    @site ? site_logical_nodes_path(@site) : logical_nodes_path
  end
end
