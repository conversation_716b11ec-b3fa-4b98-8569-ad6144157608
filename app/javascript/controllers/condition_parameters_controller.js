import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["parametersContainer"]
  static values = {
    config: Object,
    existingParameters: Object
  }

  connect() {
    console.log("ConditionParameters controller connecté")
    console.log("Configuration complète:", this.configValue)
    console.log("Paramètres existants au démarrage:", this.existingParametersValue)
    this.updateParameterFields()
  }

  getExistingParamValue(fieldName) {
    if (!this.hasExistingParametersValue || typeof this.existingParametersValue !== 'object' || this.existingParametersValue === null) {
      return undefined;
    }

    if (this.existingParametersValue[fieldName] !== undefined) {
      return this.existingParametersValue[fieldName];
    }
    const idSuffixedKey = `${fieldName}_id`;
    if (this.existingParametersValue[idSuffixedKey] !== undefined) {
      console.log(`Utilisation de ${idSuffixedKey} pour le champ ${fieldName}, valeur: ${this.existingParametersValue[idSuffixedKey]}`);
      return this.existingParametersValue[idSuffixedKey];
    }
    return undefined;
  }

  currentConditionType() {
    const conditionTypeSelect = document.querySelector("[name='condition[condition_type]']")
    return conditionTypeSelect?.value
  }

  updateParameterFields() {
    const conditionType = this.currentConditionType()

    console.log("Type sélectionné:", conditionType)

    if (!conditionType || !this.configValue || !this.configValue[conditionType]) {
      this.parametersContainerTarget.innerHTML = ""
      return
    }

    const config = this.configValue[conditionType]
    const fields = config.required_fields || []
    const fieldTypes = config.field_types || {}

    console.log("Configuration pour ce type:", config)

    let html = ""
    fields.forEach(fieldName => {
      const fieldConfig = fieldTypes[fieldName]
      if (fieldConfig) {
        html += this.generateFieldHtml(fieldName, fieldConfig)
      } else {
        console.warn(`Configuration manquante pour le champ ${fieldName}`)
      }
    })
    this.parametersContainerTarget.innerHTML = html

    this.populateAndPreselectFields(fields, fieldTypes)
    this.setupAllEventListeners(fields, fieldTypes)
  }

  generateFieldHtml(fieldName, fieldConfig) {
    const humanizedFieldName = this.humanizeFieldName(fieldName)
    if (!fieldConfig.type) {
      console.warn(`Type manquant pour le champ ${fieldName}`)
      return ''
    }

    switch (fieldConfig.type) {
      case 'select':
        return this.generateSelectField(fieldName, humanizedFieldName, fieldConfig)
      case 'text':
        return this.generateTextField(fieldName, humanizedFieldName)
      default:
        console.warn(`Type non supporté: ${fieldConfig.type} pour ${fieldName}`)
        return ''
    }
  }

  generateSelectField(fieldName, humanizedFieldName, fieldConfig) {
    let selectHtml = `
      <div class="form-group mb-3">
        <label class="form-label">${humanizedFieldName}</label>
        <select name="condition[parameters][${fieldName}]"
                class="form-select"
                ${fieldConfig.depends_on ? 'disabled' : ''}`

    if (fieldConfig.depends_on) {
      selectHtml += ` data-depends-on="${fieldConfig.depends_on}"`
    }
    selectHtml += '>'

    if (fieldConfig.choices) {
      selectHtml += '<option value="">Sélectionnez une option</option>'
      fieldConfig.choices.forEach(choice => {
        selectHtml += `<option value="${choice}">${choice}</option>`
      })
    } else {
      selectHtml += '<option value="">Chargement...</option>'
    }

    selectHtml += `
        </select>
      </div>`
    return selectHtml
  }

  generateTextField(fieldName, humanizedFieldName) {
    return `
      <div class="form-group mb-3">
        <label class="form-label">${humanizedFieldName}</label>
        <textarea name="condition[parameters][${fieldName}]"
                  class="form-control"
                  rows="4"></textarea>
      </div>`
  }

  async populateAndPreselectFields(fields, fieldTypes) {
    console.log("Peuplement et préselection avec existants:", this.existingParametersValue)

    for (const fieldName of fields) {
      const fieldConfig = fieldTypes[fieldName]
      const inputElement = this.parametersContainerTarget.querySelector(`[name="condition[parameters][${fieldName}]"]`)
      const existingValue = this.getExistingParamValue(fieldName)

      console.log(`Champ: ${fieldName}, Valeur existante extraite: ${existingValue}`)

      if (!inputElement || !fieldConfig) {
        console.warn("Élément ou configuration manquant pour:", fieldName)
        continue
      }

      if (fieldConfig.type === 'text') {
        if (existingValue !== undefined) inputElement.value = String(existingValue)
      } else if (fieldConfig.type === 'select') {
        const placeholder = fieldConfig.choices ? 'Sélectionnez une option' : `Sélectionnez ${this.humanizeFieldName(fieldName).toLowerCase()}`

        if (fieldConfig.source === "DocumentType") {
          await this.loadSelectWithOptions(inputElement, `/sites/${window.currentSiteId}/document_types`, 'id', 'name_humanized', placeholder)
          if (existingValue !== undefined) {
            inputElement.value = String(existingValue)
            console.log(`Valeur de ${fieldName} (DocumentType) définie à: "${inputElement.value}"`)
            await this.updateDependentsOf(inputElement, fieldTypes)
          }
        } else if (fieldConfig.choices) {
          if (existingValue !== undefined) {
            inputElement.value = String(existingValue)
            console.log(`Valeur de ${fieldName} (choices) définie à: "${inputElement.value}"`)
            await this.updateDependentsOf(inputElement, fieldTypes)
          }
        } else if (fieldConfig.source === "dynamic" && fieldConfig.depends_on) {
          const parentConfigName = fieldConfig.depends_on
          const parentValue = this.getExistingParamValue(parentConfigName)
          console.log(`Champ dépendant ${fieldName}, parent ${parentConfigName}, valeur parent extraite: ${parentValue}`)

          if (parentValue === undefined) {
              inputElement.innerHTML = `<option value="">Sélectionnez d'abord ${this.humanizeFieldName(parentConfigName)}</option>`
              inputElement.disabled = true
          }
        }
      }
    }
  }

  async loadSelectWithOptions(selectElement, url, valueField, textField, placeholder) {
    selectElement.innerHTML = `<option value="">${placeholder || 'Sélectionnez une option'}</option>`
    if (!url) {
        console.warn("loadSelectWithOptions appelé sans URL pour", selectElement.name)
        return;
    }
    try {
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status} pour ${url}`)
      }
      const data = await response.json()
      data.forEach(item => {
        if (item[valueField] !== undefined && item[textField] !== undefined) {
          selectElement.add(new Option(item[textField], item[valueField]))
        } else {
          console.warn("Item incomplet pour select:", item, " besoin de", valueField, textField)
        }
      })
    } catch (error) {
      console.error(`Erreur lors du chargement des options pour ${selectElement.name} depuis ${url}:`, error)
      selectElement.innerHTML = `<option value="">Erreur de chargement</option>`
    }
  }

  async updateDependentsOf(parentSelectElement, allFieldTypes) {
    const parentFieldName = parentSelectElement.name.match(/\[([^\]]+)\]$/)[1]
    const parentSelectedValue = parentSelectElement.value

    console.log(`Mise à jour des dépendants de ${parentFieldName} (valeur sélectionnée: "${parentSelectedValue}")`)

    for (const [childFieldName, childConfig] of Object.entries(allFieldTypes)) {
      if (childConfig.depends_on === parentFieldName) {
        const childSelectElement = this.parametersContainerTarget.querySelector(`[name="condition[parameters][${childFieldName}]"]`)
        if (childSelectElement) {
          console.log(`Traitement du dépendant: ${childFieldName}`)
          if (!parentSelectedValue) {
            childSelectElement.innerHTML = `<option value="">Sélectionnez d'abord ${this.humanizeFieldName(parentFieldName)}</option>`
            childSelectElement.disabled = true
          } else {
            if (parentFieldName === 'document_type' && childFieldName === 'document_field' && childConfig.source === 'dynamic') {
              await this.loadSelectWithOptions(childSelectElement, `/sites/${window.currentSiteId}/document_types/${parentSelectedValue}/fields`, 'id', 'name_humanized', 'Sélectionnez un champ')
              childSelectElement.disabled = false
              const existingChildValue = this.getExistingParamValue(childFieldName)
              console.log(`Champ enfant ${childFieldName}, valeur existante extraite: ${existingChildValue}`)
              if (existingChildValue !== undefined) {
                childSelectElement.value = String(existingChildValue)
                console.log(`Valeur de ${childFieldName} (dépendant) définie à: "${childSelectElement.value}"`)
              }
            }
          }
        }
      }
    }
  }

  setupAllEventListeners(fields, fieldTypes) {
    fields.forEach(fieldName => {
      const fieldConfig = fieldTypes[fieldName]
      if (fieldConfig && fieldConfig.type === 'select') {
        const parentSelectElement = this.parametersContainerTarget.querySelector(`[name="condition[parameters][${fieldName}]"]`)
        if (parentSelectElement) {
          const hasDependents = Object.values(fieldTypes).some(cfg => cfg.depends_on === fieldName)
          if (hasDependents) {
            parentSelectElement.addEventListener('change', (event) => this.handleParentSelectChange(event, fieldTypes))
          }
        }
      }
    })
  }

  handleParentSelectChange(event, fieldTypes) {
    const parentSelectElement = event.target
    const currentConfig = this.configValue[this.currentConditionType()]
    const currentFieldTypes = currentConfig ? currentConfig.field_types : {}
    this.updateDependentsOf(parentSelectElement, currentFieldTypes)
  }

  humanizeFieldName(fieldName) {
    const translations = {
      'document_type': 'Document concerné',
      'document_field': 'Champ concerné',
      'method_name': 'Méthode à vérifier',
      'script': 'Script personnalisé'
    }
    return translations[fieldName] || fieldName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }
}
