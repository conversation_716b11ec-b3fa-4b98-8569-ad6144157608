import { Controller } from "@hotwired/stimulus"
import TurboHelper from "utilities/turbo_helper";

// Connects to data-controller="badge-sidebar"
export default class extends Controller {
  static targets = ["sidebar", "badgeRequestTemplate", "badgeRequestsContainer", "selectedBadgesCount", "checkbox", "actionButton", "deleteButton"]
  static values = {
    fetchBadgesUrl: String,
    defaultPhotoUrl: String,
    mobileDevice: Boolean,
    sendBadgesEmailUrl: String,
    downloadBadgesUrl: String,
    deleteBadgesUrl: String,
    canForceBadge: Boolean,
  }

  connect() {
    this.setInitialState();
    this.restoreSidebarState();
    this.fetchRequestedBadges().then(() => {
      this.renderBadgeRequests();
      this.listenToCheckboxChanges();
      this.updateActionButton(false);
      this.updateDeleteButton(false);
      this.showIfBadgeSidebar();
    })
  }

  setInitialState() {
    this.badgeRequestsValue = [];
  }

  restoreSidebarState() {
    const sidebarState = localStorage.getItem('badgeSidebarVisible');
    if (sidebarState === 'true') {
      this.element.classList.add("visible");
      setTimeout(() => {
        this.syncImportSidebar();
      }, 200);
    }
  }

  saveSidebarState() {
    const isVisible = this.element.classList.contains("visible");
    localStorage.setItem('badgeSidebarVisible', isVisible);
  }

  async fetchRequestedBadges() {
    return fetch(this.fetchBadgesUrlValue)
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          this.badgeRequestsValue = data.visual_badges_requested
        } else {
          console.error("Erreur lors de la récupération des badges:", data.message)
        }
      })
  }

  renderBadgeRequests() {
    this.badgeRequestsValue.forEach(badgeRequest => {
      const template = this.badgeRequestTemplateTarget.content.cloneNode(true)
      const badgeRequestCard = template.querySelector('.badge-request-card')
      badgeRequestCard.dataset.badgeId = badgeRequest.request_id

      const photoImg = template.querySelector('.badge-request-photo img')
      if (badgeRequest.photo_url) {
        photoImg.src = badgeRequest.photo_url
      }
      photoImg.alt = `Photo de profil de ${badgeRequest.participant_name}`

      const nameDiv = template.querySelector('.badge-request-name')
      nameDiv.textContent = badgeRequest.participant_name

      const companyDiv = template.querySelector('.badge-request-company')
      companyDiv.textContent = badgeRequest.assignment_name

      const checkboxInput = template.querySelector('.badge-request-checkbox input')
      checkboxInput.id = `badge_request_${badgeRequest.request_id}`

      // Gestion des participants invalides
      if (badgeRequest.participant_valid === false) {
        // Création et ajout du tag "Non valide"
        const invalidTag = document.createElement('span')
        invalidTag.textContent = "Non valide"
        invalidTag.classList.add('indicator', 'indicator--danger', 'ms-2')

        // Insertion du tag à droite du nom
        nameDiv.innerHTML += invalidTag.outerHTML

        // Ajout d'une classe pour le style visuel
        badgeRequestCard.classList.add('badge-invalid-participant')

        // Désactiver le bouton d'action uniquement si canForceBadge est false
        this.updateActionButton(this.canForceBadgeValue)
      }

      this.badgeRequestsContainerTarget.appendChild(template)
    })
  }

  toggle() {
    this.element.classList.toggle("visible")
    setTimeout(() => {
      this.syncImportSidebar()
    }, 200)
    this.saveSidebarState();
  }

  close() {
    this.element.classList.remove("visible")
    localStorage.setItem('badgeSidebarVisible', false);
    this.syncImportSidebar();
  }

  syncImportSidebar() {
    const isVisible = this.element.classList.contains("visible");
    const importSidebar = document.getElementById("import-sidebar");

    if (importSidebar && this.badgeRequestsValue.length > 0) {
      if (isVisible) {
        importSidebar.classList.add("opacity-0");
      } else {
        importSidebar.classList.remove("opacity-0");
      }
    }
  }

  showIfBadgeSidebar() {
    if (this.badgeRequestsValue.length > 0) {
      setTimeout(() => {
        this.element.style.display = "flex";
      }, 200);
    } else {
      localStorage.setItem('badgeSidebarVisible', false);
    }
  }

  listenToCheckboxChanges() {
    const checkboxs = this.badgeRequestsContainerTarget.querySelectorAll('.badge-request-checkbox input')
    checkboxs.forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        this.updateSelectedBadgesCount(checkboxs);
      })
    })
  }

  toggleCheckbox(event) {
    // Si l'élément cliqué est la checkbox, on ne fait rien
    if (event.target.tagName === 'INPUT' && event.target.type === 'checkbox') {
      return;
    }

    // Si l'élément cliqué est la card, on toggle la checkbox
    const checkbox = event.currentTarget.querySelector('input[type="checkbox"]');
    if (checkbox && !checkbox.disabled) {
      checkbox.checked = !checkbox.checked;
      this.updateSelectedBadgesCount();
    }
  }

  updateSelectedBadgesCount() {
    const checkboxes = this.badgeRequestsContainerTarget.querySelectorAll('[data-badge-sidebar-target="checkbox"]');
    const selectedCount = [...checkboxes].filter(checkbox => checkbox.checked).length;
    this.selectedBadgesCountTarget.textContent = selectedCount;

    if (selectedCount === 0) {
      this.updateActionButton(false);
      this.updateDeleteButton(false);
    } else {
      this.updateDeleteButton(true);
      this.updateActionButton(true);
    }
  }

  updateActionButton(enabled) {
    if (this.mobileDeviceValue) {
      this.actionButtonTarget.textContent = "Envoyer par email";
    }

    const hasInvalidBadge = this.hasInvalidSelectedBadge();

    if (enabled && this.canForceBadgeValue) {
      this.actionButtonTarget.disabled = false;
      this.actionButtonTarget.classList.remove("mashe-button--disabled");
    } else if (enabled && !hasInvalidBadge) {
      this.actionButtonTarget.disabled = false;
      this.actionButtonTarget.classList.remove("mashe-button--disabled");
    } else {
      this.actionButtonTarget.disabled = true;
      this.actionButtonTarget.classList.add("mashe-button--disabled");
    }
  }

  updateDeleteButton(enabled) {
    if (enabled) {
      this.deleteButtonTarget.disabled = false;
      this.deleteButtonTarget.classList.remove("mashe-button--disabled");
    } else {
      this.deleteButtonTarget.disabled = true;
      this.deleteButtonTarget.classList.add("mashe-button--disabled");
    }
  }

  async deleteBadges() {
    const checkedBadgeIds = Array.from(this.badgeRequestsContainerTarget.querySelectorAll('.badge-request-checkbox input:checked'))
      .map(checkbox => checkbox.id.replace('badge_request_', ''));

    if (checkedBadgeIds.length === 0) {
      console.error("Aucun badge sélectionné pour la suppression");
      return;
    }

    try {
      const response = await fetch(this.deleteBadgesUrlValue, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          "X-CSRF-Token": this.getCsrfToken()
        },
        body: JSON.stringify({
          ids_list: checkedBadgeIds
        })
      });

      const data = await response.json();

      if (data.success) {
        this.removeSentBadges(checkedBadgeIds);
        TurboHelper.showNotification(data.message, "success");
      } else {
        TurboHelper.showNotification(data.message, "error");
      }
    } catch (error) {
      console.error("Erreur lors de la suppression des badges:", error);
      TurboHelper.showNotification("Erreur lors de la suppression des badges", "error");
    }
  }

  async sendBadges() {
    const checkedBadgeIds = Array.from(this.badgeRequestsContainerTarget.querySelectorAll('.badge-request-checkbox input:checked'))
      .map(checkbox => checkbox.id.replace('badge_request_', ''));

    if (checkedBadgeIds.length === 0) {
      console.error("Aucun badge sélectionné pour l'envoi");
      return;
    }
    const url = this.mobileDeviceValue ? this.sendBadgesEmailUrlValue : this.downloadBadgesUrlValue;
    try {
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRF-Token": this.getCsrfToken()
        },
        body: JSON.stringify({
          ids_list: checkedBadgeIds
        })
      });

      const data = await response.json();

      if (data.success) {
        if (data.pdf) {
          const pdfUrl = `data:application/pdf;base64,${data.pdf}`;
          const link = document.createElement('a');
          link.href = pdfUrl;
          link.download = 'badges.pdf';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }

        this.removeSentBadges(checkedBadgeIds);
        TurboHelper.showNotification(data.message, "success");
      } else {
        console.error(data);
        TurboHelper.showNotification(data.message, "error");
      }
    } catch (error) {
      console.error("Erreur lors de l'envoi des badges:", error);
      TurboHelper.showNotification("Erreur lors de l'envoi des badges", "error");
    }
  }

  removeSentBadges(badgeIds) {
    badgeIds.forEach(id => {
      const cardElement = this.badgeRequestsContainerTarget.querySelector(`.badge-request-card[data-badge-id="${id}"]`);
      if (cardElement) {
        cardElement.remove();
      }
    });

    this.updateSelectedBadgesCount();

    if (this.badgeRequestsContainerTarget.querySelectorAll('.badge-request-card').length === 0) {
      this.close();
      this.element.style.display = "none";
      localStorage.setItem('badgeSidebarVisible', false);
    }
  }

  getCsrfToken() {
    const token = document.querySelector('meta[name="csrf-token"]')?.content;
    if (!token) {
      throw new Error('Token CSRF non trouvé');
    }
    return token;
  }

  hasInvalidSelectedBadge() {
    const checkedBadgeIds = Array.from(this.badgeRequestsContainerTarget.querySelectorAll('.badge-request-checkbox input:checked'))
      .map(checkbox => checkbox.id.replace('badge_request_', ''));

    return checkedBadgeIds.some(id => {
      const cardElement = this.badgeRequestsContainerTarget.querySelector(`.badge-request-card[data-badge-id="${id}"]`);
      return cardElement && cardElement.classList.contains('badge-invalid-participant');
    });
  }
}
