import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [ "rightElementSection" ]

  connect() {
    this.handleOperatorChange(); // Call on connect to set initial state
  }

  handleOperatorChange(event) {
    const operatorSelect = event ? event.target : this.element.querySelector("select[name='logical_node[operator]']");
    if (operatorSelect.value === 'not_op') {
      this.rightElementSectionTarget.style.display = 'none';
      // Optionnel: vider les champs de l'élément droit s'ils étaient remplis
      const rightElementSelect = this.rightElementSectionTarget.querySelector("select");
      if (rightElementSelect) {
        rightElementSelect.value = '';
      }
    } else {
      this.rightElementSectionTarget.style.display = 'block';
    }
  }
}
