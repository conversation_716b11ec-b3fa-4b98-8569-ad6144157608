import { Controller } from "@hotwired/stimulus"
import TurboHelper from "utilities/turbo_helper"

export default class extends Controller {
  static targets = ["input", "results", "hiddenInput"]

  static values = {
    url: String,
    siteId: Number,
    partial: String,
    targetModel: String,
    minLength: { type: Number, default: 2 },
    debounce: { type: Number, default: 300 },
    limit: { type: Number, default: 10 },
    method: { type: String, default: "PUT" },
    autosubmit: { type: Boolean, default: true }
  }

  connect() {
    this.boundCloseResults = this.closeResults.bind(this)
    document.addEventListener("click", this.boundCloseResults)

    this.debouncedSearch = this.debounce(this.performSearch.bind(this), this.debounceValue)
  }

  disconnect() {
    document.removeEventListener("click", this.boundCloseResults)
  }

  search() {
    const query = this.inputTarget.value
    if (query.length < this.minLengthValue) {
      this.resultsTarget.innerHTML = ""
      return
    }

    this.debouncedSearch(query)
  }

  async performSearch(query) {
    const params = new URLSearchParams({
      query: query,
      site_id: this.siteIdValue,
      target_model: this.targetModelValue,
      partial_path: this.partialValue,
      limit: this.limitValue
    })

    try {
      const response = await fetch(`${this.urlValue}?${params}`, {
        headers: {
          "Accept": "text/html",
          "X-Requested-With": "XMLHttpRequest"
        }
      })

      if (!response.ok) throw new Error("Erreur réseau")

      const html = await response.text()
      this.resultsTarget.innerHTML = html
      this.resultsTarget.style.display = "block"
    } catch (error) {
      console.error("Erreur de recherche:", error)
    }
  }

  select(event) {
    const selectedElement = event.currentTarget
    const id = selectedElement.dataset.id
    const documentableType = selectedElement.dataset.documentableType
    const displayValue = selectedElement.dataset.displayValue || selectedElement.textContent.trim()

    this.inputTarget.value = displayValue
    this.hiddenInputTarget.value = id
    this.resultsTarget.style.display = "none"

    // Récupérer l'ID du document depuis l'URL du formulaire
    const form = this.element.closest('form')

    if (this.autosubmitValue) {
      // Utiliser TurboHelper pour faire une requête PUT
      TurboHelper.request(form.action, this.methodValue, {
        document: {
          documentable_id: id,
          documentable_type: documentableType
        },
        site_id: this.siteIdValue
      }).then(() => {
        // Fermer le modal de recherche s'il y en a un
        const searchExpandableElement = this.element.closest('[data-controller="search-expandable"]')
        if (searchExpandableElement) {
          const searchExpandableController = this.application.getControllerForElementAndIdentifier(
            searchExpandableElement,
            'search-expandable'
          )

          if (searchExpandableController) {
            searchExpandableController.collapse()
          }
        }
      }).catch(error => {
        console.error("Erreur lors de la mise à jour:", error)
      })

      this.dispatch("selected", {
        detail: {
          id: id,
          type: documentableType,
          value: displayValue,
          element: selectedElement
        }
      })
    }
  }

  closeResults(event) {
    if (!this.element.contains(event.target)) {
      this.resultsTarget.style.display = "none"
    }
  }

  debounce(func, wait) {
    let timeout
    return (...args) => {
      clearTimeout(timeout)
      timeout = setTimeout(() => func.apply(this, args), wait)
    }
  }
}
