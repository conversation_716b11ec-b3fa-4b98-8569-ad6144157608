import { Controller } from "@hotwired/stimulus"

/**
 * Contrôleur pour gérer les images de profil (photos, logos, etc.)
 * Permet de prévisualiser et d'envoyer via AJAX
 */
export default class extends Controller {
  static targets = ["input", "preview", "placeholder", "wrapper", "form"]
  static values = {
    modelName: String,     // Nom du modèle (ex: pending_participant, company)
    attributeName: String, // Nom de l'attribut (ex: photo, logo)
    modelId: { type: Number, default: 0 }, // ID du modèle (0 pour nouveau)
    updateUrl: { type: String, default: '' }, // URL pour l'envoi AJAX
    csrfToken: String,     // Token CSRF
    previewOnly: { type: <PERSON>olean, default: false } // Mode prévisualisation uniquement
  }

  static ALLOWED_PARTICIPANT_IMAGE_TYPES = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/webp'
  ]

  static TARGET_FILE_SIZE = 1024 * 1024
  static MAX_ORIGINAL_FILE_SIZE = 10 * 1024 * 1024
  static COMPRESSION_QUALITY = 0.8

  connect() {
    // S'assurer que le token CSRF est disponible
    this.csrfTokenValue = this.csrfTokenValue || document.querySelector("meta[name='csrf-token']")?.content

    // Créer le formData qui sera utilisé pour l'envoi
    this.formData = new FormData()

    // Trouver le formulaire parent
    this.parentForm = this.element.closest('form')

    if (this.parentForm) {
      // Écouter l'événement de soumission du formulaire
      // Utiliser une référence à la fonction liée pour pouvoir la supprimer plus tard
      this.boundHandleFormSubmit = this.handleFormSubmit.bind(this)
      this.parentForm.addEventListener('submit', this.boundHandleFormSubmit)
    }
  }

  // Gérer le changement de fichier
  async handleFileChange(event) {
    const file = event.target.files[0]
    if (!file) return

    // Vérifier si c'est une image
    if (!file.type.startsWith("image/")) {
      this.showError("Le fichier sélectionné n'est pas une image")
      return
    }


    if (!this.constructor.ALLOWED_PARTICIPANT_IMAGE_TYPES.includes(file.type)) {
      this.showError("Format d'image non autorisé. Utilisez JPG, PNG ou WebP.")
      return
    }

    if (file.size > this.constructor.MAX_ORIGINAL_FILE_SIZE) {
      this.showError("L'image est trop volumineuse. La taille maximale autorisée est de 10MB.")
      return
    }

    console.log("Fichier sélectionné:", file.name, "taille:", file.size, "type:", file.type);

    let processedFile = file

    try {
      processedFile = await this.compressImage(file)
      console.log("Image compressée:", processedFile.name, "nouvelle taille:", processedFile.size);
    } catch (error) {
      console.error("Erreur lors de la compression:", error)
      this.showError("Erreur lors de la compression de l'image")
      return
    }

    // Prévisualiser l'image
    this.showPreview(processedFile)

    // Stocker le fichier pour le formulaire principal
    this.selectedFile = processedFile

    // Si on est en mode prévisualisation uniquement, on s'arrête ici après avoir stocké le fichier
    if (this.previewOnlyValue) {
      console.log("Mode prévisualisation uniquement, le fichier sera inclus lors de la soumission du formulaire");
      // Même en mode prévisualisation, on veut que le fichier soit inclus dans le formulaire principal
      // lors de la soumission, donc on ne retourne pas tout de suite
      if (!this.updateUrlValue) {
        return
      }
    }

    // Préparer les données pour l'envoi
    this.prepareFormData(processedFile)

    // Envoyer immédiatement si on a une URL d'envoi
    if (this.updateUrlValue) {
      console.log("Envoi immédiat de l'image via AJAX");
      this.uploadImage()
    }
  }

  async compressImage(file) {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        // Calculer les nouvelles dimensions en gardant le ratio
        const maxWidth = 500
        const maxHeight = 500
        let { width, height } = img

        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width
            width = maxWidth
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height
            height = maxHeight
          }
        }

        canvas.width = width
        canvas.height = height

        // Dessiner l'image redimensionnée
        ctx.drawImage(img, 0, 0, width, height)

        // Convertir en blob avec compression
        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('Erreur lors de la compression'))
              return
            }

            // Si le fichier est encore trop gros, réduire la qualité
            if (blob.size > this.constructor.TARGET_FILE_SIZE && this.constructor.COMPRESSION_QUALITY > 0.3) {
              this.constructor.COMPRESSION_QUALITY -= 0.1
              this.compressImage(file).then(resolve).catch(reject)
              return
            }

            // Créer un nouveau fichier avec le blob compressé
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            })

            resolve(compressedFile)
          },
          file.type,
          this.constructor.COMPRESSION_QUALITY
        )
      }

      img.onerror = () => reject(new Error('Erreur lors du chargement de l\'image'))
      img.src = URL.createObjectURL(file)
    })
  }

  showError(message) {
    console.error(message)
    alert(message)
  }

  // Afficher la prévisualisation de l'image
  showPreview(file) {
    // Créer l'URL pour la prévisualisation
    const imageUrl = URL.createObjectURL(file)

    // Si une prévisualisation existe déjà
    if (this.hasPreviewTarget) {
      // Mettre à jour l'image existante
      this.previewTarget.src = imageUrl
      this.previewTarget.classList.remove("d-none")

      // Masquer le placeholder s'il existe
      if (this.hasPlaceholderTarget) {
        this.placeholderTarget.classList.add("d-none")
      }
    } else if (this.hasWrapperTarget) {
      // Si pas de prévisualisation mais un wrapper, créer une nouvelle image
      this.wrapperTarget.innerHTML = ""

      const img = document.createElement("img")
      img.src = imageUrl
      img.className = "profile-image__preview"
      img.dataset.profileImageTarget = "preview"

      this.wrapperTarget.appendChild(img)
    }

    // Stocker l'URL pour pouvoir la libérer plus tard
    this.imageUrl = imageUrl
  }

  // Préparer les données pour l'envoi
  prepareFormData(file) {
    // Réinitialiser le formData
    this.formData = new FormData()

    // Ajouter le fichier avec le nom d'attribut approprié
    // Format: model[attribute]=file
    this.formData.append(`${this.modelNameValue}[${this.attributeNameValue}]`, file)
  }

  // Envoyer l'image via AJAX
  uploadImage() {
    // Afficher un indicateur de chargement
    this.wrapperTarget.classList.add("uploading")

    fetch(this.updateUrlValue, {
      method: "PATCH",
      headers: {
        "X-CSRF-Token": this.csrfTokenValue
      },
      body: this.formData
    })
      .then(response => {
        if (!response.ok) {
          throw new Error("Erreur réseau")
        }
        return response.json()
      })
      .then(data => {
        // Traitement réussi
        console.log("Image mise à jour avec succès:", data)

        // Libérer l'URL de l'objet
        if (this.imageUrl) {
          URL.revokeObjectURL(this.imageUrl)
          this.imageUrl = null
        }

        // Si l'API renvoie une URL d'image optimisée, on l'utilise
        if (data.image_url && this.hasPreviewTarget) {
          this.previewTarget.src = data.image_url
        }
      })
      .catch(error => {
        console.error("Erreur lors de l'envoi de l'image:", error)
        // Afficher un message d'erreur à l'utilisateur si nécessaire
      })
      .finally(() => {
        // Supprimer l'indicateur de chargement
        this.wrapperTarget.classList.remove("uploading")
      })
  }

  // Méthode pour s'assurer que l'image est incluse dans le formulaire parent lors de la soumission

  // Gérer la soumission du formulaire parent
  handleFormSubmit(event) {
    // Si nous avons un fichier sélectionné et que nous sommes en mode prévisualisation
    if (this.selectedFile && this.previewOnlyValue) {
      console.log("Fichier sélectionné pour l'upload:", this.selectedFile.name);

      // Vérifier si l'input existe
      if (this.hasInputTarget) {
        try {
          // Créer un objet DataTransfer pour pouvoir assigner le fichier à l'input
          const dataTransfer = new DataTransfer();
          dataTransfer.items.add(this.selectedFile);

          // Assigner le fichier à l'input existant
          this.inputTarget.files = dataTransfer.files;

          // S'assurer que l'input n'est pas désactivé
          this.inputTarget.disabled = false;

          console.log("Fichier attaché à l'input:", this.inputTarget.files[0]?.name);
        } catch (error) {
          console.error("Erreur lors de l'attachement du fichier:", error);

          // Méthode alternative: créer un nouvel input
          const fileInput = document.createElement('input');
          fileInput.type = 'file';
          fileInput.name = `${this.modelNameValue}[${this.attributeNameValue}]`;
          fileInput.style.display = 'none';

          // Créer un objet DataTransfer pour pouvoir assigner le fichier à l'input
          const dataTransfer = new DataTransfer();
          dataTransfer.items.add(this.selectedFile);
          fileInput.files = dataTransfer.files;

          // Ajouter l'input au formulaire
          this.parentForm.appendChild(fileInput);
          console.log("Nouvel input créé avec le fichier:", fileInput.files[0]?.name);
        }
      } else {
        console.warn("Input target non trouvé, création d'un nouvel input");

        // Créer un nouvel input
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.name = `${this.modelNameValue}[${this.attributeNameValue}]`;
        fileInput.style.display = 'none';

        // Créer un objet DataTransfer pour pouvoir assigner le fichier à l'input
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(this.selectedFile);
        fileInput.files = dataTransfer.files;

        // Ajouter l'input au formulaire
        this.parentForm.appendChild(fileInput);
        console.log("Nouvel input créé avec le fichier:", fileInput.files[0]?.name);
      }
    }
  }

  // Nettoyer les ressources lors de la déconnexion du contrôleur
  disconnect() {
    if (this.imageUrl) {
      URL.revokeObjectURL(this.imageUrl)
      this.imageUrl = null
    }

    // Supprimer l'écouteur d'événement si un formulaire parent existe
    if (this.parentForm && this.boundHandleFormSubmit) {
      this.parentForm.removeEventListener('submit', this.boundHandleFormSubmit)
    }
  }
}
