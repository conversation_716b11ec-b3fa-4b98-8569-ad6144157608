import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
    static values = {
        documentId: Number
    }

    connect() {
        console.log("Participant form controller connected with document ID:", this.documentIdValue)

        // S'assurer que les boutons d'annulation sont correctement liés
        const cancelButtons = this.element.querySelectorAll('.documentable-search__button')
        console.log(`Found ${cancelButtons.length} cancel buttons`)

        cancelButtons.forEach(button => {
            // Vérifier si l'action est déjà liée
            if (!button.hasAttribute('data-action') || !button.getAttribute('data-action').includes('participant-form#cancel')) {
                console.log('Adding cancel action to button', button)
                button.setAttribute('data-action', 'participant-form#cancel')
            }
        })

        // Ajouter l'action sur le bouton submit s'il n'en a pas déjà
        const submitButton = this.element.querySelector('.mashe-button--primary')
        if (submitButton && (!submitButton.hasAttribute('data-action') || !submitButton.getAttribute('data-action').includes('participant-form#submitForm'))) {
            submitButton.setAttribute('data-action', 'participant-form#submitForm')
            console.log('Action submitForm assignée au bouton de validation')
        }
    }

    submitForm(event) {
        event.preventDefault()
        console.log('Submit button clicked, creating participant')

        // Récupérer les éléments du formulaire directement
        const firstNameInput = this.element.querySelector('input[name="participant[person_attributes][first_name]"]')
        const lastNameInput = this.element.querySelector('input[name="participant[person_attributes][last_name]"]')
        const birthDateInput = this.element.querySelector('input[name="participant[person_attributes][birth_date]"]')
        const companyIdSelect = this.element.querySelector('select[name="participant[person_attributes][company_id]"]')
        const participantRoleSiteIdSelect = this.element.querySelector('select[name="participant[participant_role_site_id]"]')
        const assignmentIdSelect = this.element.querySelector('select[name="participant[assignment_id]"]')

        // Vérifier si tous les champs sont présents
        if (!firstNameInput || !lastNameInput || !birthDateInput || !companyIdSelect ||
            !participantRoleSiteIdSelect || !assignmentIdSelect) {
            console.error('Un ou plusieurs champs requis sont manquants')
            alert('Impossible de trouver tous les champs du formulaire')
            return
        }

        // Créer un FormData manuellement
        const formData = new FormData()
        formData.append('participant[person_attributes][first_name]', firstNameInput.value)
        formData.append('participant[person_attributes][last_name]', lastNameInput.value)
        formData.append('participant[person_attributes][birth_date]', birthDateInput.value)
        formData.append('participant[person_attributes][company_id]', companyIdSelect.value)
        formData.append('participant[participant_role_site_id]', participantRoleSiteIdSelect.value)
        formData.append('participant[assignment_id]', assignmentIdSelect.value)

        // Récupérer l'ID du document et le token CSRF
        const documentId = this.documentIdValue
        const csrfToken = document.querySelector('meta[name="csrf-token"]').content

        console.log('Formulaire prêt à être envoyé:', {
            firstName: firstNameInput.value,
            lastName: lastNameInput.value,
            birthDate: birthDateInput.value,
            companyId: companyIdSelect.value,
            participantRoleSiteId: participantRoleSiteIdSelect.value,
            assignmentId: assignmentIdSelect.value
        })

        // Envoyer les données à l'API
        fetch(`/api/documents/${documentId}/create_participant`, {
            method: 'POST',
            headers: {
                'X-CSRF-Token': csrfToken,
                'Accept': 'application/json'
            },
            body: formData,
            credentials: 'same-origin'
        })
            .then(response => response.json())
            .then(data => {
                console.log('Participant creation response:', data)
                if (data.success) {
                    // Succès : charger les détails mis à jour du documentable
                    this.reloadDocumentableDetails(documentId)

                    // Afficher un message de succès adapté au type de participant créé
                    const messageType = data.documentable_type === 'PendingParticipant'
                        ? 'intervenant en attente'
                        : 'participant'
                    console.log(`Le ${messageType} a été créé avec succès.`)
                } else {
                    // Erreur : afficher le message d'erreur
                    console.error('Error creating participant:', data.error, data.details)
                    alert(`Erreur : ${data.error}`)
                }
            })
            .catch(error => {
                console.error('Network error:', error)
                alert('Erreur de réseau lors de la création du participant')
            })
    }

    reloadDocumentableDetails(documentId) {
        // Trouver le conteneur parent principal
        const documentableDetails = document.querySelector('.documentable-details')

        if (!documentableDetails) {
            console.error('No documentable-details element found')
            return
        }

        console.log('Found documentable-details element, reloading after participant creation')

        // Recharger directement les détails du documentable via AJAX
        fetch(`/documents/${documentId}/documentable_details`, {
            headers: {
                'Accept': 'text/html',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
            },
            credentials: 'same-origin'
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`)
                }
                return response.text()
            })
            .then(html => {
                console.log('Successfully fetched documentable details')
                // Remplacer tout le contenu du documentable-details
                documentableDetails.innerHTML = html
            })
            .catch(error => {
                console.error('Error reloading documentable details:', error)
                // Recharger la page comme solution de secours
                window.location.reload()
            })
    }

    cancel(event) {
        event.preventDefault()
        console.log('Cancel button clicked, event target:', event.currentTarget)

        // Trouver le conteneur parent principal
        const documentableDetails = document.querySelector('.documentable-details')

        if (!documentableDetails) {
            console.error('No documentable-details element found')
            return
        }

        console.log('Found documentable-details element')

        // Récupérer l'ID du document
        const documentId = this.documentIdValue
        console.log(`Using document ID: ${documentId}`)

        // Recharger directement les détails du documentable via AJAX
        fetch(`/documents/${documentId}/documentable_details`, {
            headers: {
                'Accept': 'text/html',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
            },
            credentials: 'same-origin'
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`)
                }
                return response.text()
            })
            .then(html => {
                console.log('Successfully fetched documentable details')
                // Remplacer tout le contenu du documentable-details
                documentableDetails.innerHTML = html
            })
            .catch(error => {
                console.error('Error reloading documentable details:', error)
                // Nous ne pouvons pas faire de fallback ici car nous avons changé l'approche
                // Recharger la page comme solution de secours
                window.location.reload()
            })
    }
}
