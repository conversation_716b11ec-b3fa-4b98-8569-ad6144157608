# Storage helper for environment-specific storage services
module StorageHelper
  # Returns the storage service (now unified with dynamic configuration)
  def self.storage_service_for_environment
    :outscale_storage
  end

  # Returns the appropriate uploads service (always the same)
  def self.uploads_service
    :outscale_uploads
  end

  # Returns the appropriate documents service (always the same)
  def self.documents_service
    :outscale_documents
  end
end
