# Storage helper for environment-specific storage services
module StorageHelper
  # Returns the appropriate storage service based on the current environment
  # For sandbox and demo environments, they share the same bucket
  def self.storage_service_for_environment
    case Rails.env
    when 'sandbox', 'demo'
      :outscale_storage_sandbox
    when 'development'
      :outscale_storage_development
    else
      :outscale_storage
    end
  end

  # Returns the appropriate uploads service (always the same)
  def self.uploads_service_for_environment
    case Rails.env
    when 'sandbox', 'demo'
      :outscale_storage_sandbox
    when 'development'
      :outscale_storage_development
    else
      :outscale_storage
    end
  end

  # Returns the appropriate documents service (always the same)
  def self.documents_service_for_environment
    case Rails.env
    when 'sandbox', 'demo'
      :outscale_storage_sandbox
    when 'development'
      :outscale_storage_development
    else
      :outscale_storage
    end
  end
end
