module RequestSourceHelper
  # Détermine la source de la requête basée sur le referer
  # @param request [ActionDispatch::Request] La requête courante
  # @param document [Document, nil] Le document pour les vérifications spécifiques
  # @return [String, nil] L'identifiant de la source
  def determine_request_source(path)
    return nil unless path

    # Sources spécifiques aux documents avec un ID
    return "participant_documents_index" if path.match?(%r{/participants/\d+/documents})
    return "assignment_documents_index" if path.match?(%r{/assignments/\d+/documents})
    # Regex plus spécifique pour uploads show : doit finir par un ID ou avoir un ID suivi de paramètres
    return "uploads_show" if path.match?(%r{/uploads/\d+(?:\?.*)?$})

    # Sources générales
    SOURCE_PATTERNS.each do |source, pattern|
      return source if path.include?(pattern)
    end

    nil
  end

  def determine_uploads_index_scope(request)
    return nil unless request

    uri = URI.parse(request.fullpath)
    query_params = URI.decode_www_form(uri.query || '').to_h
    scope = query_params['scope']
    return scope if scope.present?

    uri = URI.parse(request.referer)
    query_params = URI.decode_www_form(uri.query || '').to_h
    query_params['scope'] || "user"
  end

  def determine_uploads_index_status(request)
    return nil unless request

    uri = URI.parse(request.fullpath)
    query_params = URI.decode_www_form(uri.query || '').to_h
    status = query_params['status']
    return status if status.present?

    uri = URI.parse(request.referer)
    query_params = URI.decode_www_form(uri.query || '').to_h
    query_params['status'] || "pending"
  end

  # Configuration des patterns de sources
  SOURCE_PATTERNS = {
    "uploads_index" => "/uploads",
    "assignments_show" => "/assignments/",
    "participants_show" => "/participants/",
    "pending_participants_show" => "/pending_participants/"
  }.freeze
end
