module DocumentF<PERSON><PERSON><PERSON><PERSON><PERSON>
  def render_document_field_input(form, field, extracted_field)
    label = field.humanized_name
    label += " (admin)" unless field.displayed?
    options = {
      label: label,
      input_html: {
        data: { action: "change->auto-submit#submit" },
        value: extracted_field.value
      }
    }

    render_input_by_format(form, field, extracted_field.value, options)
  end

  def render_input_by_format(form, field, value, options = {})
    if field.format.to_sym == :list && field.has_subfields?
      render_list_with_subfields(form, field, OpenStruct.new(value: value))
    else
      input_options = build_input_options_for_format(field, value, options)
      form.input :value, input_options
    end
  end

  def build_input_options_for_format(field_or_subfield, value, options = {})
    # raise
    format = field_or_subfield.format.to_sym
    options[:input_html] ||= {}

    case format
    when :date
      if value.present? && value != "null" && value.is_a?(String)
        parsed_value = Toolbox::DateTools::DateParser.parse(value)&.strftime("%d/%m/%Y")
      else
        parsed_value = nil
      end
      options[:as] = :string
      options[:input_html][:class] = "form-control"
      options[:input_html][:value] = parsed_value
      options[:input_html][:data] = (options[:input_html][:data] || {}).merge({
                                                                                controller: "datepicker",
                                                                                datepicker_default_date_value: parsed_value
                                                                              })
    when :boolean
      options[:as] = :boolean
      options[:input_html][:checked] = ["true", true].include?(value)
    when :float, :integer
      options[:as] = :numeric
      options[:input_html][:step] = format == :float ? "0.01" : "1"
    when :list
      options[:as] = :text
      options[:input_html][:class] = "form-select"
      options[:input_html][:rows] = 4
    when :country
      options[:as] = :select
      options[:collection] = Country.all.map { |c| [c.name, c.iso_code] }
      options[:selected] = value
      options[:include_blank] = true
    when :choice
      options[:as] = :select
      options[:collection] = field_or_subfield.document_field_options.map { |o| [o.label, o.key] }
      options[:selected] = value
      options[:include_blank] = true
    else # :string par défaut
      options[:as] = :string
    end

    options
  end

  def render_list_with_subfields(form, field, extracted_field)
    items = parse_hybrid_json_ruby(extracted_field.value.to_s)

    content_tag(:div, class: "mashe-list-field-wrapper", data: { controller: "list-items" }) do
      label = label_tag field.name, field.humanized_name, class: "form-label"

      list_container = content_tag(:div, class: "mashe-list-items-container",
                                         data: { 'list-items-target': "itemsContainer", field_id: field.id }) do
        items_html = items.map.with_index do |item, index|
          render_list_item(field, item, index)
        end
        safe_join(items_html)
      end

      add_button = content_tag(:button, "Ajouter un élément",
                               type: "button",
                               class: "mashe-button mashe-button--primary mashe-button--sm mt-2",
                               data: { action: "click->list-items#addItem" })

      hidden_field = form.input :value,
                                as: :hidden,
                                input_html: {
                                  value: JSON.generate(items),
                                  data: { 'list-items-target': "value" }
                                }

      template = content_tag(:template, data: { 'list-items-target': "template" }) do
        empty_item = field.document_subfields.each_with_object({}) { |sf, hash| hash[sf.name.to_s] = nil }
        render_list_item(field, empty_item, "__INDEX__")
      end

      safe_join([label, list_container, add_button, hidden_field, template])
    end
  end

  def render_list_item(field, item, index)
    index_str = index.to_s
    content_tag(:div, class: "mashe-list-item", data: { index: index_str }) do
      delete_btn = content_tag(:button, "×",
                               type: "button",
                               class: "mashe-list-item__delete-btn",
                               aria: { label: "Supprimer l'élément #{index_str == '__INDEX__' ? 'template' : index_str.to_i + 1}" },
                               data: { action: "click->list-items#removeItem", index: index_str })

      subfields_container = content_tag(:div, class: "mashe-list-item__subfields") do
        subfields_html = field.document_subfields.map do |subfield|
          render_subfield_input(subfield, item[subfield.name.to_s], index_str)
        end
        safe_join(subfields_html)
      end

      safe_join([delete_btn, subfields_container])
    end
  end

  def render_subfield_input(subfield, value, index)
    field_name = "subfield[#{index}][#{subfield.name}]"
    field_id = "subfield_#{index}_#{subfield.name}".parameterize.underscore

    input_html = {
      class: "inherit-inputs",
      data: {
        action: "change->list-items#updateSubfieldValue",
        'list-items-target': "subfieldInput",
        'list-items-index-param': index,
        'list-items-name-param': subfield.name
      },
      id: field_id
    }

    options = build_input_options_for_format(subfield, value, input_html: input_html)

    options[:input_html][:data] = input_html[:data].merge(options[:input_html][:data] || {})

    content_tag(:div, class: "mashe-subfield", data: { subfield_name: subfield.name }) do
      label = content_tag(:label, subfield.humanized_name, for: field_id, class: "form-label")
      input = create_input_from_options(field_name, value, options)
      safe_join([label, input])
    end
  end

  def create_input_from_options(field_name, value, options)
    input_type = options[:as]
    options[:input_html] ||= {}
    options[:input_html][:class] = "#{options[:input_html][:class]} form-control".strip

    case input_type
    when :string
      text_field_tag(field_name, value, options[:input_html])
    when :boolean
      content_tag(:div, class: "form-check") do
        check_box_tag(field_name, "true", options[:input_html][:checked],
                      options[:input_html].except(:checked).merge(class: "form-check-input")) +
          label_tag(options[:input_html][:id], "", class: "form-check-label")
      end
    when :numeric
      number_field_tag(field_name, value, options[:input_html])
    when :text
      options[:input_html][:rows] ||= 3
      text_area_tag(field_name, value, options[:input_html])
    when :select
      unless options[:input_html][:class]&.include?('form-select')
        options[:input_html][:class] =
          "#{options[:input_html][:class]} form-select".strip
      end
      select_tag(field_name,
                 options_for_select(options[:collection], options[:selected]),
                 options[:input_html].merge(include_blank: options[:include_blank]))
    else
      text_field_tag(field_name, value, options[:input_html])
    end
  end

  def display_country_value(value)
    return "" if value.blank?

    country = Country.find_by(iso_code: value)
    country ? country.name : value
  end

  def display_choice_value(value, document_field)
    return "" if value.blank?

    option = document_field.document_field_options.find_by(key: value)
    option ? option.label : value
  end

  def parse_hybrid_json_ruby(str)
    return [] if str.blank?

    json_string = str.gsub('=>', ':').gsub('nil', 'null')
    JSON.parse(json_string)
  end
end
