# == Schema Information
#
# Table name: stakeholders
#
#  id                    :integer          not null, primary key
#  construction_board_id :integer          not null
#  name                  :string           not null
#  role                  :string
#  logo                  :string
#  other_infos           :jsonb
#  address               :string
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#
# Indexes
#
#  index_stakeholders_on_construction_board_id  (construction_board_id)
#

class Stakeholder < ApplicationRecord
  belongs_to :construction_board
  has_one :site, through: :construction_board

  validates :name, presence: { message: "Ne peut pas être vide" }
  validates :role, presence: { message: "Ne peut pas être vide" }
  validates :address, presence: { message: "Ne peut pas être vide" }
  validate :other_infos_must_not_be_empty

  has_one_attached :logo, service: StorageService.storage_service

  def full_details
    "#{name} (#{role})"
  end

  def site_id
    construction_board.site_id
  end

  private

  def other_infos_must_not_be_empty
    return unless other_infos.present?

    other_infos.each do |key, value|
      if key.blank? || value.blank?
        errors.add(:other_infos, "Les informations complémentaires ne peuvent pas être vides")
      end
    end
  end
end
