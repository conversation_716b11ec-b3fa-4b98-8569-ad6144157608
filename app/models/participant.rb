# == Schema Information
#
# Table name: participants
#
#  id                       :integer          not null, primary key
#  person_id                :integer          not null
#  assignment_id            :integer          not null
#  creator_type             :string
#  creator_id               :integer
#  participant_role_site_id :integer
#  is_valid                 :boolean          default("false"), not null
#  valid_from               :date
#  valid_until              :date
#  qr_code                  :string
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#
# Indexes
#
#  index_participants_on_assignment_id             (assignment_id)
#  index_participants_on_creator                   (creator_type,creator_id)
#  index_participants_on_participant_role_site_id  (participant_role_site_id)
#  index_participants_on_person_id                 (person_id)
#

class Participant < ApplicationRecord
  include Searchable
  include Verifiable
  include Bypassable
  include Vettable
  include DocumentAssociable
  include ValidityTrigger

  trigger_validity_update_on_change_of :participant_role_site_id

  has_one_attached :photo, service: StorageService.storage_service
  # Associations principales
  belongs_to :person
  belongs_to :assignment
  belongs_to :participant_role_site, optional: true
  belongs_to :creator, polymorphic: true, optional: true

  has_many :equipment_donations, dependent: :destroy
  has_many :group_results, as: :subject, dependent: :destroy
  has_many :locker_participants, dependent: :destroy
  has_many :own_documents, as: :documentable, dependent: :nullify, class_name: 'Document'
  has_many :participant_qualifications, dependent: :destroy
  has_many :site_control_entries, dependent: :destroy
  has_many :site_control_incidents, dependent: :destroy
  has_many :safety_session_signatures, dependent: :destroy
  has_many :visual_badge_requests, dependent: :destroy
  has_many :key_movements

  has_one :company, through: :assignment
  has_one :employer, through: :person, source: :company
  delegate :interim?, to: :employer
  has_one :participant_role, through: :participant_role_site
  has_one :site, through: :assignment
  has_one :external_identifier, dependent: :destroy

  has_many :failed_condition_records, through: :group_results
  has_many :site_qualification_requirements, through: :participant_qualifications
  has_many :qualification_catalogs, through: :participant_qualifications
  has_many :presence_movements, dependent: :destroy

  # Délégations
  delegate :full_name, :first_name, :last_name, :birth_date, to: :person
  alias name full_name
  delegate :name, to: :employer, prefix: true
  delegate :name, to: :participant_role_site, prefix: true
  delegate :company_name, to: :assignment, prefix: true

  before_create :verify_if_company_person_exists
  after_create :grant_interim_access_if_needed
  before_destroy :sync_external_deletion

  validates :participant_role_site_id, presence: true
  validate :validate_company_assignment_correspondence
  validate :validate_photo_content_type

  before_validation :set_default_participant_role_site, on: :create

  accepts_nested_attributes_for :person

  # Scopes
  scope :of_site, ->(site_id) { joins(:assignment).where(assignments: { site_id: site_id }) }

  has_one :company_person,
          lambda {
            joins(person: { participants: { assignment: :site } })
              .where(company_people: { company_id: Site.select(:company_id) })
              .distinct
          },
          through: :person,
          source: :company_people

  has_many :company_person_qualifications,
           lambda {
             left_joins(company_person: { person: { participants: { assignment: :site } } })
               .where(company_people: { company_id: Site.select(:company_id) })
               .distinct
           },
           through: :company_person,
           source: :person_qualifications

  scope :with_all_site_data, lambda { |site_id|
    includes(
      :person,
      { participant_role_site: :participant_role },
      { assignment: :company },
      { group_results: [:condition_group] },
      { person: { company_people: :person_qualifications } },
      :participant_qualifications,
      { company_person_qualifications: :qualification_catalog }
    )
      .references(
        :participant_qualifications,
        :participant_role_site,
        :participant_role,
        :assignment,
        :company,
        :group_results,
        :condition_group,
        :company_people,
        :person_qualifications,
        :company_person_qualifications
      )
      .joins(:assignment)
      .joins(person: :company_people)
      .joins("INNER JOIN sites ON sites.id = assignments.site_id")
      .where(assignments: { site_id: site_id })
      .where(company_people: { company_id: Site.select(:company_id) })
  }

  def all_qualifications
    company_person_qualifications + participant_qualifications
  end

  def on_site?(site)
    site.id == assignment.site_id
  end

  def locker_numbers_formatted
    locker_participants.map(&:locker_number).join(", ")
  end

  def generate_qrcode_uuid
    update(qr_code: SecureRandom.uuid_v7)
    qr_code
  end

  def role_context
    @role_context = RoleContext.find_by(
      assignment_role_site: assignment.assignment_role_site,
      participant_role_site: participant_role_site
    ) || RoleContext.find_by(
      assignment_role_site: nil,
      participant_role_site: participant_role_site
    ) || RoleContext.find_by(
      assignment_role_site: assignment.assignment_role_site,
      participant_role_site: nil
    )
  end

  def has_access?
    return true if access_granted_by_bypass?
    return false if access_denied_by_bypass?

    access_granted_by_vetting? && valid_at?
  end

  def site_role_condition_groups
    @site_role_condition_groups ||= role_context&.site_role_condition_groups
  end

  def penalty_account
    @penalty_account ||= PenaltyAccount.find_or_create_by!(
      person_id: person_id,
      site_id: assignment.site_id
    )
  end

  def condition_groups
    @condition_groups ||= site_role_condition_groups&.map(&:condition_group)
  end

  DocumentType.find_each do |document_type|
    # Définir la méthode singulière (retourne un seul document)
    define_method(document_type.name) do
      documents.find_by(document_type_id: document_type.id)
    end

    # Définir la méthode plurielle (retourne tous les documents)
    define_method(document_type.name.pluralize) do
      documents.where(document_type_id: document_type.id)
    end
  end

  def documents_names
    documents.map(&:document_type_name)
  end

  def participant_role_site_short_name
    participant_role_site&.short_name
  end

  def group_results_short_names
    group_results.map(&:condition_group).map(&:short_name).compact
  end

  def valid_indicator
    is_valid? ? "Autorisé" : "Refusé"
  end

  # Méthode documents qui combine les documents
  def documents
    Document.where(
      '(documentable_type = ? AND documentable_id = ?) OR (documentable_type = ? AND documentable_id = ?)',
      'Participant', id,
      'CompanyPerson', company_person.id
    )
  end

  # Méthode pour trouver un participant par son numéro de carte BTP
  def self.find_by_btp_card_number(btp_card_number)
    QueryManager.call(
      "participant.find_by_btp_card_number",
      btp_card_number: btp_card_number
    )
  end

  def currently_on_site?
    PresenceMovement.last_movement_is_entry?(id)
  end

  # Création rapide d'une entrée sur site
  def register_entry(user: nil, event_at: Time.current)
    presence_movements.create!(
      movement_type: :in_site,
      movement_at: event_at,
      user: user
    )
  end

  # Création rapide d'une sortie du site
  def register_exit(user: nil, event_at: Time.current)
    presence_movements.create!(
      movement_type: :out_site,
      movement_at: event_at,
      user: user
    )
  end

  private

  def define_document_methods
    self.class.define_document_type_methods
  end

  def verify_if_company_person_exists
    current_assignment = assignment
    return unless current_assignment&.site

    site_company_id = current_assignment.site.company_id
    existing_company_person = CompanyPerson.find_by(person_id: person_id, company_id: site_company_id)
    return if existing_company_person.present?

    company_person_person = person || Person.find_by(id: person_id)
    return unless company_person_person

    CompanyPerson.create!(person: company_person_person, company: current_assignment.site.company)
  end

  def set_default_participant_role_site
    current_assignment = assignment
    return unless participant_role_site_id.nil?

    self.participant_role_site_id = current_assignment&.site&.default_participant_role_site&.id
  end

  def grant_interim_access_if_needed
    interim_company = person.company
    # Vérifier si c'est une entreprise d'intérim
    return unless interim_company.interim?

    # Vérifier si l'accès existe déjà
    return if InterimAccess.where(
      interim_company: interim_company,
      accessible: assignment
    ).exists?

    # Créer l'accès automatiquement
    InterimAccess.create!(
      interim_company: interim_company,
      accessible: assignment,
      creator_type: nil,
      creator_id: nil
    )
  end

  def validate_company_assignment_correspondence
    # Ensure participant_role_site and its participant_role exist
    return unless participant_role_site&.participant_role
    # Check if the role requires the assignment company to be the same as the employer
    return unless participant_role_site.participant_role.same_company?
    # Ensure person, employer, assignment, assignment's company and site exist
    return unless person&.company && assignment&.company && assignment.site

    # If the assignment's company doesn't match the person's employer
    return unless person.company_id != assignment.company_id

    # Try to find the correct assignment (employer's assignment on this site)
    correct_assignment = Assignment.find_by(
      company_id: person.company_id,
      site_id: assignment.site_id
    )

    other_possible_assignment = Assignment.find_by(
      company_id: assignment.company_id,
      site_id: assignment.site_id
    )

    if correct_assignment
      # If found, update the participant's assignment
      self.assignment = correct_assignment
    elsif other_possible_assignment
      self.assignment = other_possible_assignment
    else
      # If no correct assignment is found, add an error
      errors.add(:assignment_id, :company_mismatch_not_found,
                 message: "doit correspondre à l'employeur (le rôle l'exige) et aucun assignment correspondant n'a été trouvé pour l'employeur sur ce site")
    end
  end

  def validate_photo_content_type
    return unless photo.attached?

    allowed_types = %w[image/jpeg image/jpg image/png image/webp]

    unless photo.content_type.in?(allowed_types)
      errors.add(:photo, "doit être une image au format JPG, PNG ou WebP")
    end

    # Vérifier la taille du fichier (optionnel, 10MB max)
    if photo.byte_size > 10.megabytes
      errors.add(:photo, "ne doit pas dépasser 10MB")
    end
  end

  def sync_external_deletion
    ExternalApis::ParticipantDeletionService.new(self).call
  end
end
