# == Schema Information
#
# Table name: companies
#
#  id                   :integer          not null, primary key
#  name                 :string
#  company_number       :string
#  address              :string
#  interim              :boolean
#  creator_type         :string
#  creator_id           :integer
#  can_perform_controls :boolean          default("false")
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#
# Indexes
#
#  index_companies_on_creator  (creator_type,creator_id)
#

class Company < ApplicationRecord
  include PgSearch::Model
  include FeatureControllable
  include SettingControllable
  include DocumentAssociable

  has_one :company_setting, dependent: :destroy

  pg_search_scope :search,
                  against: %i[name company_number],
                  using: {
                    tsearch: {
                      prefix: true,
                      dictionary: 'french'
                    },
                    trigram: {
                      threshold: 0.6
                    }
                  }

  belongs_to :creator, polymorphic: true, optional: true

  has_many :assignments, dependent: :destroy
  has_many :people, dependent: :destroy
  has_many :conditions, dependent: :destroy
  has_many :pending_participants, dependent: :destroy
  has_many :sites, dependent: :destroy
  has_many :users, dependent: :destroy

  has_one_attached :logo, service: Rails.env.sandbox? ? :outscale_storage_sandbox : :outscale_storage
  has_many :qualification_catalogs, dependent: :destroy

  has_many :source_company_connections, class_name: 'CompanyConnection',
                                        foreign_key: 'source_company_id', dependent: :destroy
  has_many :target_company_connections, class_name: 'CompanyConnection',
                                        foreign_key: 'target_company_id', dependent: :destroy

  has_many :known_companies, through: :source_company_connections, source: :target_company
  has_many :signable_templates, as: :owner, dependent: :destroy

  has_many :interim_accesses, foreign_key: :interim_company_id, dependent: :destroy, inverse_of: :interim_company
  has_many :accessible_assignments, through: :interim_accesses, source: :accessible, source_type: 'Assignment'
  has_many :accessible_companies, through: :interim_accesses, source: :accessible, source_type: 'Company'
  has_many :company_interim_accesses, as: :accessible, class_name: 'InterimAccess', dependent: :destroy
  has_many :authorized_interim_companies, through: :company_interim_accesses, source: :interim_company

  validates :name, :company_number, presence: true
end
