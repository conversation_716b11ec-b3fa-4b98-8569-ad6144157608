# == Schema Information
#
# Table name: sanctions
#
#  id                 :integer          not null, primary key
#  user_id            :integer          not null
#  sanction_type_id   :integer          not null
#  points_sanctioned  :integer          default("1")
#  comment            :text
#  penalty_account_id :integer          not null
#  assignment_id      :integer          not null
#  sanction_date      :datetime
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#
# Indexes
#
#  index_sanctions_on_assignment_id       (assignment_id)
#  index_sanctions_on_penalty_account_id  (penalty_account_id)
#  index_sanctions_on_sanction_type_id    (sanction_type_id)
#  index_sanctions_on_user_id             (user_id)
#

class Sanction < ApplicationRecord
  attr_accessor :participant

  has_one_attached :image, service: StorageHelper.storage_service_for_environment

  belongs_to :user
  belongs_to :penalty_account
  belongs_to :assignment
  has_one :site, through: :penalty_account
  has_one :person, through: :penalty_account

  belongs_to :sanction_type

  before_validation :ensure_penalty_account, on: :create
  before_validation :ensure_assignment, on: :create

  # Callback de gestion des points restants
  after_create :deduct_points
  after_destroy :restore_points

  delegate :name, to: :sanction_type, prefix: true
  delegate :full_name, to: :person, prefix: true
  delegate :full_name, to: :user, prefix: true

  def deduct_points
    penalty_account.update_points_remaining(-points_sanctioned)
  end

  def restore_points
    penalty_account.update_points_remaining(points_sanctioned)
  end

  def ensure_assignment
    self.assignment ||= participant.assignment
  end

  private

  def ensure_penalty_account
    # Vérifie que `participant` est fourni
    unless participant.present? || penalty_account.present?
      raise ArgumentError,
            "Participant is required to create a Sanction"
    end

    self.penalty_account ||= PenaltyAccount.find_or_create_by!(
      person_id: participant.person_id, # ID de la personne du participant
      site_id: participant.assignment.site_id # ID du site via l'affectation du participant
    )
  end
end
