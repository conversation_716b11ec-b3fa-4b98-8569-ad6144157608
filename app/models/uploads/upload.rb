# == Schema Information
#
# Table name: uploads
#
#  id                 :integer          not null, primary key
#  user_id            :integer          not null
#  site_id            :integer
#  status             :integer          default("0")
#  import_method      :integer          default("0")
#  admin_review       :boolean          default("false")
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  perimeterable_type :string
#  perimeterable_id   :integer
#
# Indexes
#
#  index_uploads_on_perimeterable  (perimeterable_type,perimeterable_id)
#  index_uploads_on_site_id        (site_id)
#  index_uploads_on_user_id        (user_id)
#

class Upload < ApplicationRecord
  belongs_to :user
  belongs_to :site, optional: true
  has_many :documents, dependent: :destroy
  has_many :upload_issues, dependent: :destroy
  has_many :upload_logs, dependent: :destroy
  belongs_to :perimeterable, polymorphic: true, optional: true
  has_one_attached :file, service: StorageService.uploads_service

  delegate :full_name, to: :user, prefix: true
  delegate :filename, to: :file, prefix: false

  enum :status, {
    pending: 0,
    processing: 1,
    completed: 2,
    validated: 3,
    failed: 4
  }

  enum :import_method, {
    file_upload: 0,
    smartphone: 1,
    email: 2
  }

  def extract_documents
    transaction do
      documents.destroy_all
      Documents::ProcessingService.new(self).process
    end
  end

  def status_verbose
    {
      pending: "En attente",
      processing: "En cours",
      completed: "A valider",
      validated: "Validé",
      failed: "Échec"
    }[status.to_sym]
  end

  def import_method_verbose
    {
      file_upload: "Fichier",
      smartphone: "Téléphone",
      email: "Email"
    }[import_method.to_sym]
  end

  def name
    if documents.any?
      documents.map(&:content_description).join(", ")
    else
      file.filename.to_s
    end
  end

  def truncated_name
    name.truncate(60)
  end

  def issues?
    upload_issues.any?
  end

  def issue_summary
    upload_issues.group(:issue_type).count
  end
end
