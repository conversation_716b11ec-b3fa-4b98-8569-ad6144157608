# == Schema Information
#
# Table name: sites
#
#  id         :integer          not null, primary key
#  name       :string
#  short_name :string
#  address    :string
#  company_id :integer          not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
# Indexes
#
#  index_sites_on_company_id  (company_id)
#

class Site < ApplicationRecord
  include FeatureControllable
  include SettingControllable

  attr_accessor :access_level

  belongs_to :company
  has_many :assignments
  has_many :uploads
  has_many :companies, through: :assignments
  has_many :scopes
  has_many :participants, through: :assignments
  has_many :people, through: :participants
  has_many :user_sites, dependent: :destroy
  has_many :users, through: :user_sites
  has_many :assignments_referents, through: :user_sites
  has_many :referents, through: :assignments_referents, source: :users
  has_many :lockers, dependent: :destroy
  has_many :locker_damages, through: :lockers
  has_many :participant_group_results, through: :participants, source: :group_results
  has_many :assignment_group_results, through: :assignments, source: :group_results
  has_many :occupied_lockers,
           -> { joins(:active_locker_participants) },
           class_name: 'Locker'

  has_many :sanctions, through: :assignments
  has_many :site_equipments, dependent: :destroy
  has_many :equipment_donations, through: :participants
  has_many :safety_sessions, dependent: :destroy

  has_one :site_point_quota, dependent: :destroy
  has_one_attached :locker_plan, service: StorageHelper.storage_service_for_environment

  has_many :participant_role_sites, dependent: :destroy
  has_many :assignment_role_sites, dependent: :destroy

  has_many :participant_roles, through: :participant_role_sites
  has_many :assignment_roles, through: :assignment_role_sites

  has_many :participant_role_contexts, through: :participant_role_sites, source: :role_contexts
  has_many :assignment_role_contexts, through: :assignment_role_sites, source: :role_contexts

  has_many :site_qualification_requirements, dependent: :destroy
  has_many :qualification_catalogs, through: :site_qualification_requirements

  has_one :site_detail, dependent: :destroy
  has_one :construction_board, dependent: :destroy
  has_many :lots, dependent: :destroy
  has_many :stakeholders, through: :construction_board
  has_many :site_controls, dependent: :destroy
  has_many :site_control_incidents, dependent: :destroy
  has_many :site_meetings, dependent: :destroy
  has_many :feature_mappings, as: :controllable, dependent: :destroy
  has_many :features, through: :feature_mappings

  has_many :signable_templates, as: :owner, dependent: :destroy
  has_many :api_users, as: :authorizable, dependent: :destroy
  has_many :external_api_configurations, through: :api_users

  accepts_nested_attributes_for :feature_mappings, allow_destroy: true
  accepts_nested_attributes_for :site_detail

  validates :address, presence: true
  validates :name, presence: true
  validates :short_name, presence: true

  has_many :pending_participants

  def create_main_company_assignment
    Assignments::MainCompanyAssignmentCreator.new.create(self)
  end

  scope :internal_users, lambda { |site|
    User.joins(:user_sites).where(user_sites: { site_id: site.id }).where(company_id: site.company.id)
  }

  def role_contexts
    RoleContext.where(
      id: (participant_role_contexts.pluck(:id) + assignment_role_contexts.pluck(:id)).uniq
    )
  end

  def update_validity_status
    assignments.each(&:update_validity_status)
    participants.each(&:update_validity_status)
    pending_participants.each(&:update_validity_status)
  end

  def overloaded_qualifications_ids
    qualification_catalogs.pluck(:id)
  end

  def default_participant_role_site
    @default_participant_role_site ||= ParticipantRoleSite.find_by(
      site: self,
      participant_role: ParticipantRole.find_by(name: get_setting("participants", "default_role_name"))
    )
  end

  def default_assignment_role_site
    @default_assignment_role_site ||= AssignmentRoleSite.find_by(
      site: self,
      assignment_role: AssignmentRole.find_by(name: get_setting("assignments", "default_role_name"))
    )
  end

  def cached_internal_users
    @cached_internal_users ||= Site.internal_users(self)
  end

  def cached_internal_users_emails
    @cached_internal_users_emails ||= Site.internal_users(self).pluck(:email)
  end
end
