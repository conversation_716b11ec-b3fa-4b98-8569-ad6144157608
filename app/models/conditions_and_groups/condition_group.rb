# == Schema Information
#
# Table name: condition_groups
#
#  id             :integer          not null, primary key
#  humanized_name :string
#  name           :string
#  short_name     :string
#  is_mandatory   :boolean          default("true")
#  target_type    :integer          default("0"), not null
#  purpose        :integer          default("0"), not null
#  global         :boolean          default("false")
#  company_id     :integer
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#
# Indexes
#
#  index_condition_groups_on_company_id  (company_id)
#

class ConditionGroup < ApplicationRecord
  attr_accessor :target_scope, :per_site

  has_many :site_role_condition_groups, dependent: :destroy
  belongs_to :company, optional: true

  has_many :condition_group_mappings, dependent: :destroy
  has_many :conditions, through: :condition_group_mappings, source: :element, source_type: "Condition"
  has_many :logical_nodes, through: :condition_group_mappings, source: :element, source_type: 'LogicalNode'
  has_many :group_results, dependent: :destroy

  enum :target_type, { participant: 0, assignment: 1, person: 2, company: 3 }
  enum :purpose, { role: 0, qualification: 1 }

  def condition_names
    conditions.pluck(:name)
  end

  def all_element_names
    # Récupère tous les éléments (Condition ou LogicalNode) via les mappings
    # et prend leur attribut 'name'.
    # Eager load les éléments pour éviter les N+1 requêtes.
    condition_group_mappings.includes(:element).map(&:element).compact.map do |element|
      "#{element.class.model_name.human}: #{element.name}"
    end
  end

  def target_type_humanized
    case target_type
    when "participant", "person"
      "Intervenant"
    when "assignment", "company"
      "Entreprise"
    end
  end

  def evaluate(verifiable)
    Conditions::GroupEvaluator.new(verifiable).evaluate(self)
  end

  def verify_for(verifiable)
    Conditions::GroupVerifier.new(verifiable).verify_group(self)
  end

  def document_types
    (conditions.flat_map(&:document_types) + logical_nodes.flat_map(&:document_types)).uniq
  end
end
