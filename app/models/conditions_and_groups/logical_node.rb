# == Schema Information
#
# Table name: logical_nodes
#
#  id                 :integer          not null, primary key
#  operator           :integer          not null
#  name               :string           not null
#  left_element_type  :string           not null
#  left_element_id    :integer          not null
#  right_element_type :string
#  right_element_id   :integer
#  fallback_message   :string
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#
# Indexes
#
#  index_logical_nodes_on_left_element   (left_element_type,left_element_id)
#  index_logical_nodes_on_right_element  (right_element_type,right_element_id)
#

class LogicalNode < ApplicationRecord
  belongs_to :left_element, polymorphic: true
  belongs_to :right_element, polymorphic: true, optional: true

  has_many :failed_condition_records, as: :element, dependent: :destroy

  enum :operator, { and_op: 0, or_op: 1, not_op: 2 }

  validates :operator, presence: true
  validates :left_element, presence: true
  validates :right_element, presence: true, if: :requires_right_element?
  validates :right_element, absence: { message: "must be empty for NOT operator" }, if: :not_op?

  validate :ensure_no_circular_dependencies

  def evaluate(context)
    Conditions::LogicalNodeEvaluator.new(self).evaluate(context)
  end

  def flatten_conditions
    LogicalNodeRepository.flatten_conditions(self, preload_parameters: true)
  end

  # Méthode pour afficher la structure du nœud logique dans la console
  def to_readable_tree(indent = 0)
    prefix = "  " * indent

    case operator
    when "and_op"
      result = "#{prefix}ET [\n"
      result += "#{left_element.respond_to?(:to_readable_tree) ? left_element.to_readable_tree(indent + 1) : "#{prefix}  #{element_description(left_element)}"}\n"
      result += "#{right_element.respond_to?(:to_readable_tree) ? right_element.to_readable_tree(indent + 1) : "#{prefix}  #{element_description(right_element)}"}\n"
      result += "#{prefix}]"
    when "or_op"
      result = "#{prefix}OU [\n"
      result += "#{left_element.respond_to?(:to_readable_tree) ? left_element.to_readable_tree(indent + 1) : "#{prefix}  #{element_description(left_element)}"}\n"
      result += "#{right_element.respond_to?(:to_readable_tree) ? right_element.to_readable_tree(indent + 1) : "#{prefix}  #{element_description(right_element)}"}\n"
      result += "#{prefix}]"
    when "not_op"
      result = "#{prefix}NON [\n"
      result += "#{left_element.respond_to?(:to_readable_tree) ? left_element.to_readable_tree(indent + 1) : "#{prefix}  #{element_description(left_element)}"}\n"
      result += "#{prefix}]"
    end

    result
  end

  def document_type
    left_element.document_type
  end

  def document_types
    [left_element.document_type, right_element&.document_type].compact
  end

  def operator_display
    case operator
    when 'and_op' then 'ET'
    when 'or_op'  then 'OU'
    when 'not_op' then 'NON'
    else operator.to_s.humanize
    end
  end

  def left_element_display_name
    left_element&.name
  end

  def right_element_display_name
    right_element&.name
  end

  private

  def requires_right_element?
    and_op? || or_op?
  end

  def element_description(element)
    if element.is_a?(Condition)
      "Condition: #{element.name} (#{element.parameters_type})"
    else
      "Élément: #{element.class.name} ##{element.id}"
    end
  end

  def ensure_no_circular_dependencies
    # Vérifier pour l'élément gauche
    if left_element.is_a?(LogicalNode) && descendant_creates_cycle?(left_element, self)
      errors.add(:left_element_id, "crée une dépendance circulaire")
    end

    # Vérifier pour l'élément droit (s'il est pertinent)
    return unless right_element.is_a?(LogicalNode) && operator != 'not_op'
    return unless descendant_creates_cycle?(right_element, self)

    errors.add(:right_element_id, "crée une dépendance circulaire")
  end

  # Méthode récursive pour vérifier si un 'start_node' (un descendant potentiel)
  # a 'target_node_being_saved' (le noeud actuel `self`) dans sa propre descendance.
  # 'visited_nodes_in_path' est utilisé pour éviter les boucles infinies LORS DE CETTE VÉRIFICATION
  # si le graphe de dépendance a déjà une boucle qui n'implique pas 'target_node_being_saved'.
  def descendant_creates_cycle?(current_descendant, target_node_being_saved, visited_nodes_in_path = Set.new)
    # Cas de base 1: Le descendant est le nœud que nous essayons de sauvegarder. Cycle trouvé !
    # Comparaison par instance si l'un ou l'autre n'est pas persisté, sinon par ID.
    if current_descendant == target_node_being_saved || (current_descendant.id.present? && current_descendant.id == target_node_being_saved.id)
      return true
    end

    # Si current_descendant n'est pas persisté (et n'est pas target_node_being_saved),
    # ou si nous l'avons déjà visité dans ce chemin de vérification, arrêtons cette branche.
    return false if current_descendant.id.nil? || visited_nodes_in_path.include?(current_descendant.id)

    visited_nodes_in_path.add(current_descendant.id) # Marquer comme visité pour ce chemin

    cycle_found = false
    # Vérifier le sous-élément gauche de current_descendant
    if current_descendant.left_element.is_a?(LogicalNode)
      cycle_found ||= descendant_creates_cycle?(current_descendant.left_element, target_node_being_saved,
                                                visited_nodes_in_path)
    end

    # Si pas encore trouvé, vérifier le sous-élément droit de current_descendant
    if !cycle_found && current_descendant.right_element.is_a?(LogicalNode) && current_descendant.operator != 'not_op'
      cycle_found ||= descendant_creates_cycle?(current_descendant.right_element, target_node_being_saved,
                                                visited_nodes_in_path)
    end

    visited_nodes_in_path.delete(current_descendant.id) # Backtrack pour DFS
    cycle_found
  end
end
