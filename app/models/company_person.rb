# == Schema Information
#
# Table name: company_people
#
#  id         :integer          not null, primary key
#  company_id :integer          not null
#  person_id  :integer          not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
# Indexes
#
#  index_company_people_on_company_id  (company_id)
#  index_company_people_on_person_id   (person_id)
#

class CompanyPerson < ApplicationRecord
  include Searchable
  belongs_to :company
  belongs_to :person
  has_one_attached :photo, service: StorageHelper.storage_service_for_environment

  has_many :sites,
           ->(company_person) { where(company_id: company_person.company_id) },
           through: :person

  has_many :participants,
           lambda { |company_person|
             joins(assignment: :site)
               .where(sites: { company_id: company_person.company_id })
           },
           through: :person
  has_many :person_qualifications, dependent: :destroy
  has_many :qualification_catalogs, through: :person_qualifications

  has_many :documents, as: :documentable

  delegate :full_name, :first_name, :last_name, :birth_date, to: :person
  alias name full_name

  scope :on_site, ->(site_id) { joins(:participants, :assignment).where(participants: { assignment: { site_id: } }) }

  include Verifiable

  def pprint
    puts "CompanyPerson: id: #{id}, person_id: #{person_id}, company_id: #{company_id}"
  end

  def update_validity_status
    participants.each(&:update_validity_status)
  end

  def participant_for(site)
    participants.find_by(assignment: { site: site })
  end
end
