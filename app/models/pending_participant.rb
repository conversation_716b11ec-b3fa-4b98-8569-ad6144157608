# == Schema Information
#
# Table name: pending_participants
#
#  id                       :integer          not null, primary key
#  first_name               :string
#  last_name                :string
#  birth_date               :date
#  is_interim               :boolean          default("false")
#  company_id               :integer
#  assignment_id            :integer
#  site_id                  :integer          not null
#  participant_role_site_id :integer
#  is_valid                 :boolean          default("false")
#  converted_at             :datetime
#  status                   :integer          default("0"), not null
#  participant_id           :integer
#  valid_from               :date
#  valid_until              :date
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#  user_id                  :integer
#
# Indexes
#
#  index_pending_participants_on_assignment_id             (assignment_id)
#  index_pending_participants_on_company_id                (company_id)
#  index_pending_participants_on_converted_at              (converted_at)
#  index_pending_participants_on_participant_id            (participant_id)
#  index_pending_participants_on_participant_role_site_id  (participant_role_site_id)
#  index_pending_participants_on_site_id                   (site_id)
#  index_pending_participants_on_status                    (status)
#  index_pending_participants_on_user_id                   (user_id)
#

class PendingParticipant < ApplicationRecord
  include Searchable
  include Verifiable
  include DocumentAssociable

  VALIDITY_ATTRS = %i[company_id assignment_id participant_role_site_id]

  belongs_to :company, optional: true
  belongs_to :assignment, optional: true
  belongs_to :site
  belongs_to :participant_role_site, optional: true
  belongs_to :participant, optional: true
  belongs_to :user, optional: true
  has_one :person, through: :participant
  has_one :participant_role, through: :participant_role_site
  has_one :employer, through: :person, source: :company
  validates :participant_role_site_id, presence: true

  # Validation pour s'assurer de la cohérence entre le rôle et les entreprises
  validate :validate_company_assignment_correspondence

  before_validation :set_default_participant_role_site, on: :create
  before_save :format_names

  delegate :name, to: :participant_role_site, prefix: true
  delegate :name, to: :company, prefix: true, allow_nil: true
  delegate :company_name, to: :assignment, prefix: true, allow_nil: true

  has_one_attached :photo, service: StorageHelper.storage_service_for_environment
  # Enum pour le statut du pending_participant
  # enum :status, { jesaispas: 0, processed: 1, rejected: 2, archived: 3 }

  # Scope pour filtrer par statut
  scope :active, -> { where(status: :active) }
  scope :processed, -> { where(status: :processed) }
  scope :not_converted, -> { where(converted_at: nil) }
  scope :converted, -> { where.not(converted_at: nil) }

  # Scope pour filtrer par site
  scope :of_site, ->(site_id) { joins(:assignment).where(assignments: { site_id: site_id }) }

  alias_attribute :interim, :is_interim

  def full_name
    "#{first_name} #{last_name}"
  end
  alias name full_name

  def has_access?
    valid_at?
  end

  def format_names
    # Traitement du prénom : split sur espaces/virgules, titleize chaque partie et rejoindre avec un espace
    self.first_name = first_name.to_s.strip.split(/[\s,]+/).map do |part|
      # Gestion spéciale pour les prénoms composés avec tiret
      part.split('-').map(&:capitalize).join('-')
    end.join(', ')

    self.last_name = last_name.strip.upcase
  end

  def role_context
    if assignment.present?
      # Préchargement des associations nécessaires en une seule requête
      RoleContext.find_by(
        assignment_role_site: assignment.assignment_role_site,
        participant_role_site: participant_role_site
      ) ||
        RoleContext.find_by(
          assignment_role_site: nil,
          participant_role_site: participant_role_site
        ) ||
        RoleContext.find_by(
          assignment_role_site: assignment.assignment_role_site,
          participant_role_site: nil
        )
    else
      # Requête optimisée pour le cas sans assignment
      default_role_name = site.get_setting("assignments", "default_role_name")

      RoleContext.joins(assignment_role_site: :assignment_role)
                 .find_by(
                   participant_role_site: participant_role_site,
                   assignment_roles: { name: default_role_name },
                   assignment_role_sites: { site_id: site.id }
                 )
    end
  end

  def convert!(user: nil)
    PendingParticipantConverterService.new(self, user:, site:).convert
  end

  def site_role_condition_groups
    @site_role_condition_groups ||= role_context&.site_role_condition_groups
  end

  def condition_groups
    @condition_groups ||= site_role_condition_groups&.map(&:condition_group)
  end

  def valid_indicator
    is_valid? ? "Autorisé" : "Refusé"
  end

  def converted?
    converted_at.present?
  end

  def has_completed_informations?
    first_name.present? && last_name.present? && birth_date.present? &&
      participant_role_site_id.present? && company_id.present? && !assignment_missing?
  end

  def has_minimal_informations?
    first_name.present? && last_name.present? && company_id.present?
  end

  # Retourne un tableau des messages décrivant les informations manquantes
  def missing_information_messages
    messages = []
    messages << "Prénom manquant" if first_name.blank?
    messages << "Nom manquant" if last_name.blank?
    messages << "Date de naissance manquante" if birth_date.blank?
    messages << "Rôle manquant" if participant_role_site_id.blank?
    messages << "Entreprise manquante" if company_id.blank?
    messages << "Affectation manquante" if assignment_missing?
    messages
  end

  def assignment_missing?
    assignment_id.blank? && company_id.blank? && !participant_role_site.participant_role.same_company
  end

  DocumentType.find_each do |document_type|
    # Définir la méthode singulière (retourne un seul document)
    define_method(document_type.name) do
      documents.find_by(document_type_id: document_type.id)
    end

    # Définir la méthode plurielle (retourne tous les documents)
    define_method(document_type.name.pluralize) do
      documents.where(document_type_id: document_type.id)
    end
  end

  def documents
    Document.where(
      '(documentable_type = ? AND documentable_id = ?)',
      'PendingParticipant', id
    )
  end

  def initials
    "#{first_name[0]}#{last_name[0]}"
  end

  private

  def validate_company_assignment_correspondence
    # Ensure participant_role_site and its participant_role exist
    return unless participant_role_site&.participant_role
    # Check if the role requires the assignment company to be the same as the employer
    return unless participant_role_site.participant_role.same_company?
    # Ensure person, employer, assignment, assignment's company and site exist
    return unless person&.company && assignment&.company && assignment.site

    # If the assignment's company doesn't match the person's employer
    return unless person.company_id != assignment.company_id

    # Try to find the correct assignment (employer's assignment on this site)
    correct_assignment = Assignment.find_by(
      company_id: person.company_id,
      site_id: assignment.site_id
    )

    other_possible_assignment = Assignment.find_by(
      company_id: assignment.company_id,
      site_id: assignment.site_id
    )

    if correct_assignment
      # If found, update the participant's assignment
      self.assignment = correct_assignment
    elsif other_possible_assignment
      self.assignment = other_possible_assignment
    else
      # If no correct assignment is found, add an error
      errors.add(:assignment_id, :company_mismatch_not_found,
                 message: "doit correspondre à l'employeur (le rôle l'exige) et aucun assignment correspondant n'a été trouvé pour l'employeur sur ce site")
    end
  end

  def set_default_participant_role_site
    self.participant_role_site_id ||= site.default_participant_role_site&.id
  end
end
