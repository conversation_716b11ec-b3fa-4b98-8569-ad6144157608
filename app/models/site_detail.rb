# == Schema Information
#
# Table name: site_details
#
#  id            :integer          not null, primary key
#  site_id       :integer          not null
#  floor_area    :integer
#  permit_number :string
#  description   :text
#  site_image    :string
#  permit_date   :date
#  base_email    :string
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#
# Indexes
#
#  index_site_details_on_site_id  (site_id)
#

class SiteDetail < ApplicationRecord
  attr_accessor :skip_validations

  after_create :create_reference, unless: :seeding?
  after_update :update_reference, if: -> { saved_change_to_base_email? && !seeding? }
  before_destroy :delete_reference, unless: :seeding?

  belongs_to :site

  has_one_attached :site_image, service: StorageService.storage_service

  validates :site_id, uniqueness: true
  validates :floor_area, numericality: { only_integer: true, greater_than: 0 }, unless: -> { skip_validations }
  validates :permit_number, presence: { message: "Ne peut pas être vide" }, if: lambda {
    permit_date.present? && !skip_validations
  }
  validates :permit_date, presence: { message: "Ne peut pas être vide" }, unless: -> { skip_validations }
  validates :description, presence: { message: "Ne peut pas être vide" }, unless: -> { skip_validations }
  validates :base_email, uniqueness: true, if: -> { base_email.present? }

  delegate :name, :short_name, :address, to: :site, prefix: false, allow_nil: true

  private

  def seeding?
    Rails.application.config.seeding
  end

  def create_reference
    Echo::ReferenceManagerService.new(model: site, status: "created").call
  end

  def update_reference
    Echo::ReferenceManagerService.new(model: site, status: "updated").call
  end

  def delete_reference
    Echo::ReferenceManagerService.new(model: site, status: "deleted").call
  end
end
