# == Schema Information
#
# Table name: key_sets
#
#  id           :integer          not null, primary key
#  location     :string
#  name         :string
#  uuid         :string
#  content      :string
#  status       :integer
#  creator_type :string
#  creator_id   :integer
#  site_id      :integer          not null
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#
# Indexes
#
#  index_key_sets_on_creator            (creator_type,creator_id)
#  index_key_sets_on_location_and_name  (location,name)
#  index_key_sets_on_site_id            (site_id)
#  index_key_sets_on_uuid               (uuid) UNIQUE
#

class KeySet < ApplicationRecord
  has_paper_trail versions: {
    scope: -> { order("id desc") }
  }, only: %i[status], on: %i[update], skip: proc { |key_set|
    # Ne pas enregistrer les versions si seuls les attributs name ou location sont modifiés
    key_set.saved_changes.keys.all? { |attr| %w[name location updated_at].include?(attr) }
  }

  belongs_to :site
  belongs_to :creator, polymorphic: true

  has_one_attached :photo, service: StorageService.storage_service

  has_many :key_movements, dependent: :destroy

  enum :status, {
    available: 0,
    unavailable: 1,
    lost: 2,
    returned: 3
  }

  validates :location, :name, :creator, :site, presence: true
  validates :location, uniqueness: {
    scope: :name,
    message: "doit être unique pour ce nom"
  }

  validates :uuid, uniqueness: true, allow_nil: true

  before_validation :set_uuid, on: :create

  scope :available, -> { where(status: :available) }
  scope :unavailable, -> { where(status: :unavailable) }
  scope :on_site, ->(site_id) { where(site_id: site_id) }

  def current_participant
    key_movements&.last&.participant
  end

  def status?(status, operator = :or)
    status = [status] unless status.is_a?(Array)
    if operator == :or
      status.any? { |s| self.status == s }
    else
      status.all? { |s| self.status == s }
    end
  end

  def status_translated
    {
      "available" => "Disponible",
      "unavailable" => "Prêté",
      "lost" => "Perdu",
      "returned" => "Retourné"
    }[status]
  end

  def assign_to(participant)
    return raise ActiveRecord::RecordNotFound, "Participant not found" if participant.site != site

    ActiveRecord::Base.transaction do
      key_movements.create!(
        participant: participant,
        movement_type: :assigned,
        movement_date: Time.current
      )
      update!(status: :unavailable)
    end
  end

  def unassign_from(participant)
    return raise ActiveRecord::RecordNotFound, "Participant not found" if participant.site != site

    key_movement = key_movements.last
    return raise ActiveRecord::RecordNotFound, "Key movement not found" if key_movement.nil?

    ActiveRecord::Base.transaction do
      key_movement.update!(
        movement_type: :unassigned,
        returned_by: participant,
        participant: nil,
        return_date: Time.current
      )
      update!(status: :available)
    end
  end

  def return_to_origin
    ActiveRecord::Base.transaction do
      key_movements.create!(
        movement_type: :returned,
        return_date: Time.current,
        movement_date: Time.current
      )
      update!(status: :returned)
    end
  end

  def return_from_origin
    ActiveRecord::Base.transaction do
      key_movements.create!(
        movement_type: :received,
        movement_date: Time.current
      )
      update!(status: :available)
    end
  end

  def lost
    ActiveRecord::Base.transaction do
      key_movements.create!(
        movement_type: :lost,
        movement_date: Time.current,
        participant: current_participant
      )
      update!(status: :lost)
    end
  end

  def unlost
    ActiveRecord::Base.transaction do
      key_movements.create!(
        movement_type: :unlost,
        movement_date: Time.current,
        participant: current_participant
      )
      if current_participant.present?
        update!(status: :unavailable)
      else
        update!(status: :available)
      end
    end
  end

  # Traduction de l'attribut location pour les erreurs de validation
  def self.human_attribute_name(attribute_name, options = {})
    if attribute_name == "location"
      "Localisation"
    else
      super
    end
  end

  private

  def set_uuid
    retries = 3
    begin
      self.uuid = SecureRandom.random_number(1_000_000_000_000_000_000)
    rescue ActiveRecord::RecordNotUnique
      retries -= 1
      retry if retries.positive?
    end
  end
end
