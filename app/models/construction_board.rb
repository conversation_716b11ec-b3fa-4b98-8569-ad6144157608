# == Schema Information
#
# Table name: construction_boards
#
#  id          :integer          not null, primary key
#  site_id     :integer          not null
#  enabled     :boolean          default("true")
#  uuid        :string           not null
#  other_infos :jsonb
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#
# Indexes
#
#  index_construction_boards_on_site_id  (site_id)
#  index_construction_boards_on_uuid     (uuid) UNIQUE
#

class ConstructionBoard < ApplicationRecord
  belongs_to :site
  has_many :stakeholders, dependent: :destroy

  has_many_attached :images, service: StorageService.storage_service

  validates :uuid, presence: true, uniqueness: true
  validates :enabled, inclusion: { in: [true, false] }
end
