# == Schema Information
#
# Table name: people
#
#  id           :integer          not null, primary key
#  first_name   :string
#  last_name    :string
#  company_id   :integer          not null
#  birth_date   :date
#  creator_type :string
#  creator_id   :integer
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#
# Indexes
#
#  index_people_on_company_id  (company_id)
#  index_people_on_creator     (creator_type,creator_id)
#

class Person < ApplicationRecord
  include Searchable

  belongs_to :company
  belongs_to :creator, polymorphic: true, optional: true

  has_many :participants, dependent: :destroy
  has_many :sites, through: :participants
  has_many :penalty_accounts, dependent: :destroy
  has_many :company_people, dependent: :destroy
  has_many :documents, as: :documentable, dependent: :destroy

  before_create :format_names

  def formatted_birthdate
    birth_date.strftime("%d/%m/%Y") if birth_date
  end

  def format_names
    self.first_name = first_name.strip.split(/[,\s]+/).map do |part|
      part.split('-').map(&:capitalize).join('-')
    end.join(', ')
    self.last_name = last_name.strip.upcase
  end

  def full_name
    "#{first_name} #{last_name}"
  end
  alias name full_name

  def initials
    "#{first_name[0]}#{last_name[0]}"
  end
end
