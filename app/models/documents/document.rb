# == Schema Information
#
# Table name: documents
#
#  id                  :integer          not null, primary key
#  upload_id           :integer
#  document_type_id    :integer
#  documentable_type   :string
#  documentable_id     :integer
#  scope_id            :integer
#  reviewer_id         :integer
#  initial_document_id :integer
#  status              :integer          default("0")
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#
# Indexes
#
#  index_documents_on_document_type_id     (document_type_id)
#  index_documents_on_documentable         (documentable_type,documentable_id)
#  index_documents_on_initial_document_id  (initial_document_id)
#  index_documents_on_reviewer_id          (reviewer_id)
#  index_documents_on_scope_id             (scope_id)
#  index_documents_on_upload_id            (upload_id)
#

class Document < ApplicationRecord
  belongs_to :upload, optional: true
  has_one :site, through: :upload
  has_one :user, through: :upload
  belongs_to :document_type, optional: true
  belongs_to :documentable, polymorphic: true, optional: true
  belongs_to :scope, optional: true
  belongs_to :reviewer, class_name: 'User', optional: true
  belongs_to :initial_document, class_name: 'Document', optional: true
  has_many :document_fields, through: :document_type
  has_many :extracted_fields, dependent: :destroy
  delegate :name, to: :document_type, prefix: true
  delegate :target_model, to: :document_type

  has_one_attached :file, service: StorageHelper.documents_service

  enum :status, { active: 0, obsolete: 1, rejected: 2, archived: 3 }
  after_create :apply_obsolescence_strategy
  after_update :update_documentable_validity, if: :saved_change_to_documentable?

  validates :upload, presence: true, if: :imported_document?
  has_many :document_associations, dependent: :destroy

  # Raccourcis pratiques pour récupérer les enregistrements associés par type
  has_many :associated_participants,
           through: :document_associations,
           source: :associated_record,
           source_type: 'Participant'

  has_many :associated_companies,
           through: :document_associations,
           source: :associated_record,
           source_type: 'Company'

  has_many :associated_assignments,
           through: :document_associations,
           source: :associated_record,
           source_type: 'Assignment'

  def signature_service
    @signature_service ||= Documents::DocumentSignatureService.new(self)
  end

  # Méthodes de délégation pour conserver l'interface
  delegate :signable?, :signed?, :signature_date, :signature_metadata,
           :template, :template_version, :rendered_content, :sign!,
           to: :signature_service

  def filename
    file.filename.to_s
  end

  def person
    documentable if documentable_type == "Person"
  end

  def person!
    person || raise("Person not found for document #{id}")
  end

  def company
    documentable if documentable_type == "Company"
  end

  def company!
    company || raise("Company not found for document #{id}")
  end

  def archive!
    update(status: :archived)
  end

  def personal_document?
    ["person", "participant"].include?(document_type&.target_type)
  end

  def documentable_attributes
    fields_hash = extracted_fields.each_with_object({}) do |field, hash|
      hash[field.document_field.name] = field.value
    end

    fields_hash.merge(document_type: document_type)
  end

  def resolve
    DocumentableResolver::Resolver.new(documentable_attributes, site, user).resolve
  end

  def content_description
    string = ""
    string += "#{document_type.humanized_name}" if document_type
    string += " de " if document_type && documentable
    string += "#{documentable.name}" if documentable
    string
  end

  def pprint
    to_print = "Document #{id} - #{document_type_name}"
    extracted_fields.each do |field|
      to_print += "\n  #{field.document_field.name}: #{field.value}"
    end
    puts to_print
  end

  def method_missing(method_name, *args, &)
    # Vérifie d'abord si c'est une méthode de classe
    if self.class == method_name.to_s.classify
      # Recherche le document_type par son name
      document_type = DocumentType.find_by!(name: method_name.to_s)
      return Document.where(document_type: document_type)
    end

    # Le reste de votre logique existante pour les méthodes d'instance
    field = document_fields.find_by(name: method_name)
    if field
      extracted_fields.find_by(document_field: field)
    else
      super
    end
  end

  def self.method_missing(method_name, *args, &)
    # Recherche le document_type par son name
    document_type = DocumentType.find_by!(name: method_name.to_s)
    where(document_type: document_type)
  rescue ActiveRecord::RecordNotFound
    super
  end

  def apply_obsolescence_strategy
    return unless document_type&.obsolescence_strategy

    document_type.obsolescence_strategy.apply(self)
  end

  def imported_document?
    document_type.present? && document_type.document_category == 'imported'
  end

  private

  def saved_change_to_documentable?
    saved_change_to_documentable_id? || saved_change_to_documentable_type?
  end

  # Met à jour le statut de validité de l'ancien et du nouveau documentable
  # si celui-ci a changé.
  def update_documentable_validity
    old_documentable_id = documentable_id_before_last_save
    old_documentable_type = documentable_type_before_last_save

    if old_documentable_id && old_documentable_type
      # Récupère l'ancien enregistrement polymorphe
      old_documentable = old_documentable_type.constantize.find_by(id: old_documentable_id)
      # Vérifie si les objets répondent à la méthode avant de l'appeler pour éviter les erreurs
      old_documentable&.update_validity_status if old_documentable&.respond_to?(:update_validity_status)
    end

    documentable&.update_validity_status if documentable&.respond_to?(:update_validity_status)
  end
end
