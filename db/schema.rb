# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source <PERSON>s uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_05_22_161038) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "fuzzystrmatch"
  enable_extension "pg_catalog.plpgsql"
  enable_extension "pg_trgm"

  # Custom types defined in this database.
  # Note that some types may not work with other database engines. Be careful if changing database.
  create_enum "locker_key_movement_type", ["ASSIGNMENT", "LOST", "RETURN"]

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "api_keys", force: :cascade do |t|
    t.string "name", null: false
    t.string "key_value", null: false
    t.datetime "key_expiration", null: false
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "api_permissions", force: :cascade do |t|
    t.bigint "api_user_id", null: false
    t.integer "permission_type", default: 0, null: false
    t.string "route", null: false
    t.jsonb "constraints", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["api_user_id"], name: "index_api_permissions_on_api_user_id"
  end

  create_table "api_users", force: :cascade do |t|
    t.string "authenticator_type", null: false
    t.bigint "authenticator_id", null: false
    t.string "authorizable_type"
    t.bigint "authorizable_id"
    t.integer "footprint_app_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "admin"
    t.index ["authenticator_id", "authenticator_type"], name: "index_api_users_on_api_key_auth", where: "((authenticator_type)::text = 'ApiKey'::text)"
    t.index ["authenticator_id", "authenticator_type"], name: "index_api_users_on_credential_auth", where: "((authenticator_type)::text = 'Credential'::text)"
    t.index ["authenticator_type", "authenticator_id"], name: "index_api_users_on_authenticator"
    t.index ["authorizable_id", "authorizable_type"], name: "index_api_users_on_company_auth", where: "((authorizable_type)::text = 'Company'::text)"
    t.index ["authorizable_id", "authorizable_type"], name: "index_api_users_on_site_auth", where: "((authorizable_type)::text = 'Site'::text)"
    t.index ["authorizable_type", "authorizable_id"], name: "index_api_users_on_authorizable"
    t.check_constraint "authorizable_type IS NULL OR (authorizable_type::text = ANY (ARRAY['Site'::character varying::text, 'Company'::character varying::text]))", name: "check_authorizable_type"
  end

  create_table "assignment_lots", force: :cascade do |t|
    t.bigint "lot_id", null: false
    t.bigint "assignment_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["assignment_id"], name: "index_assignment_lots_on_assignment_id"
    t.index ["lot_id", "assignment_id"], name: "index_assignment_lots_on_lot_id_and_assignment_id", unique: true
    t.index ["lot_id"], name: "index_assignment_lots_on_lot_id"
  end

  create_table "assignment_role_categories", force: :cascade do |t|
    t.string "name"
    t.string "description"
    t.string "code"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "assignment_role_sites", force: :cascade do |t|
    t.bigint "assignment_role_id", null: false
    t.bigint "site_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["assignment_role_id"], name: "index_assignment_role_sites_on_assignment_role_id"
    t.index ["site_id"], name: "index_assignment_role_sites_on_site_id"
  end

  create_table "assignment_roles", force: :cascade do |t|
    t.string "name"
    t.string "description"
    t.string "short_name"
    t.bigint "company_id"
    t.boolean "global", default: false
    t.bigint "assignment_role_category_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["assignment_role_category_id"], name: "index_assignment_roles_on_assignment_role_category_id"
    t.index ["company_id"], name: "index_assignment_roles_on_company_id"
  end

  create_table "assignments", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.bigint "site_id", null: false
    t.bigint "assignment_parent_id"
    t.string "creator_type"
    t.bigint "creator_id"
    t.bigint "assignment_role_site_id"
    t.date "start_date"
    t.boolean "is_valid", default: false, null: false
    t.date "valid_from"
    t.date "valid_until"
    t.boolean "pending", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["assignment_parent_id"], name: "index_assignments_on_assignment_parent_id"
    t.index ["assignment_role_site_id"], name: "index_assignments_on_assignment_role_site_id"
    t.index ["company_id"], name: "index_assignments_on_company_id"
    t.index ["creator_type", "creator_id"], name: "index_assignments_on_creator"
    t.index ["site_id"], name: "index_assignments_on_site_id"
  end

  create_table "assignments_referents", force: :cascade do |t|
    t.bigint "assignment_id", null: false
    t.bigint "user_site_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["assignment_id"], name: "index_assignments_referents_on_assignment_id"
    t.index ["user_site_id"], name: "index_assignments_referents_on_user_site_id"
  end

  create_table "association_exists_parameters", force: :cascade do |t|
    t.bigint "document_type_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["document_type_id"], name: "index_association_exists_parameters_on_document_type_id"
  end

  create_table "attendees", force: :cascade do |t|
    t.string "first_name"
    t.string "last_name"
    t.string "email"
    t.string "uuid"
    t.boolean "send_access_code", default: true
    t.datetime "access_code_sent_at"
    t.datetime "authorized_from"
    t.datetime "authorized_to"
    t.datetime "scanned_at"
    t.bigint "site_meeting_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["site_meeting_id"], name: "index_attendees_on_site_meeting_id"
  end

  create_table "boolean_parameters", force: :cascade do |t|
    t.bigint "document_type_id", null: false
    t.bigint "document_field_id", null: false
    t.string "type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["document_field_id"], name: "index_boolean_parameters_on_document_field_id"
    t.index ["document_type_id"], name: "index_boolean_parameters_on_document_type_id"
  end

  create_table "bypasses", force: :cascade do |t|
    t.string "bypassable_type", null: false
    t.bigint "bypassable_id", null: false
    t.bigint "created_by_id", null: false
    t.integer "bypass_type", default: 1
    t.date "bypass_start"
    t.date "bypass_end"
    t.text "reason"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["bypassable_type", "bypassable_id"], name: "index_bypasses_on_bypassable"
    t.index ["created_by_id"], name: "index_bypasses_on_created_by_id"
  end

  create_table "companies", force: :cascade do |t|
    t.string "name"
    t.string "company_number"
    t.string "address"
    t.boolean "interim"
    t.string "creator_type"
    t.bigint "creator_id"
    t.boolean "can_perform_controls", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["creator_type", "creator_id"], name: "index_companies_on_creator"
  end

  create_table "company_connections", force: :cascade do |t|
    t.bigint "source_company_id", null: false
    t.bigint "target_company_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["source_company_id"], name: "index_company_connections_on_source_company_id"
    t.index ["target_company_id"], name: "index_company_connections_on_target_company_id"
  end

  create_table "company_people", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.bigint "person_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_company_people_on_company_id"
    t.index ["person_id"], name: "index_company_people_on_person_id"
  end

  create_table "company_settings", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.integer "max_user_session_timeout_seconds"
    t.integer "default_user_session_timeout_seconds"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_company_settings_on_company_id", unique: true
  end

  create_table "condition_group_mappings", force: :cascade do |t|
    t.bigint "condition_group_id", null: false
    t.string "element_type", null: false
    t.bigint "element_id", null: false
    t.integer "position"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["condition_group_id"], name: "index_condition_group_mappings_on_condition_group_id"
    t.index ["element_type", "element_id"], name: "index_condition_group_mappings_on_element"
  end

  create_table "condition_groups", force: :cascade do |t|
    t.string "humanized_name"
    t.string "name"
    t.string "short_name"
    t.boolean "is_mandatory", default: true
    t.integer "target_type", default: 0, null: false
    t.integer "purpose", default: 0, null: false
    t.boolean "global", default: false
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_condition_groups_on_company_id"
  end

  create_table "conditions", force: :cascade do |t|
    t.string "name"
    t.string "description"
    t.string "fallback_message"
    t.string "parameters_type"
    t.bigint "parameters_id"
    t.integer "purpose", default: 0, null: false
    t.integer "target_type", default: 0, null: false
    t.bigint "company_id"
    t.boolean "global", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_conditions_on_company_id"
    t.index ["parameters_type", "parameters_id"], name: "index_conditions_on_parameters"
  end

  create_table "construction_boards", force: :cascade do |t|
    t.bigint "site_id", null: false
    t.boolean "enabled", default: true
    t.string "uuid", null: false
    t.jsonb "other_infos"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["site_id"], name: "index_construction_boards_on_site_id"
    t.index ["uuid"], name: "index_construction_boards_on_uuid", unique: true
  end

  create_table "contacts", force: :cascade do |t|
    t.bigint "assignment_id", null: false
    t.string "name"
    t.string "email"
    t.string "phone"
    t.string "function"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["assignment_id"], name: "index_contacts_on_assignment_id"
  end

  create_table "countries", force: :cascade do |t|
    t.string "iso_code", null: false
    t.string "name", null: false
    t.string "demonym"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["iso_code"], name: "index_countries_on_iso_code", unique: true
    t.index ["name"], name: "index_countries_on_name"
  end

  create_table "credentials", force: :cascade do |t|
    t.string "login", null: false
    t.string "password", null: false
    t.string "password_digest", null: false
    t.string "password_salt", null: false
    t.string "token"
    t.datetime "token_expiry"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "custom_parameters", force: :cascade do |t|
    t.bigint "document_type_id", null: false
    t.string "custom_method_name", null: false
    t.string "catalog_name", default: "default", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "document_field_id"
    t.index ["document_field_id"], name: "index_custom_parameters_on_document_field_id"
    t.index ["document_type_id"], name: "index_custom_parameters_on_document_type_id"
  end

  create_table "date_parameters", force: :cascade do |t|
    t.bigint "document_type_id", null: false
    t.bigint "document_field_id", null: false
    t.string "type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["document_field_id"], name: "index_date_parameters_on_document_field_id"
    t.index ["document_type_id"], name: "index_date_parameters_on_document_type_id"
  end

  create_table "default_user_assignment_roles", force: :cascade do |t|
    t.bigint "user_role_id", null: false
    t.bigint "assignment_role_id", null: false
    t.string "perimeter_type"
    t.bigint "perimeter_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["assignment_role_id"], name: "index_default_user_assignment_roles_on_assignment_role_id"
    t.index ["perimeter_type", "perimeter_id"], name: "index_default_user_assignment_roles_on_perimeter"
    t.index ["user_role_id", "assignment_role_id", "perimeter_id", "perimeter_type"], name: "index_default_user_assignment_roles_unique", unique: true
    t.index ["user_role_id"], name: "index_default_user_assignment_roles_on_user_role_id"
  end

  create_table "document_associations", force: :cascade do |t|
    t.bigint "document_id", null: false
    t.string "associated_record_type", null: false
    t.bigint "associated_record_id", null: false
    t.string "role"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["associated_record_type", "associated_record_id"], name: "index_document_associations_on_associated_record"
    t.index ["document_id", "associated_record_type", "associated_record_id"], name: "index_doc_assoc_on_doc_and_assoc", unique: true
    t.index ["document_id"], name: "index_document_associations_on_document_id"
  end

  create_table "document_field_options", force: :cascade do |t|
    t.string "key", null: false
    t.string "label", null: false
    t.bigint "document_field_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["document_field_id", "key"], name: "index_document_field_options_on_document_field_id_and_key", unique: true
    t.index ["document_field_id"], name: "index_document_field_options_on_document_field_id"
  end

  create_table "document_fields", force: :cascade do |t|
    t.bigint "document_type_id", null: false
    t.string "name"
    t.string "humanized_name"
    t.text "description"
    t.boolean "displayed", default: true
    t.integer "format", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "unique_identifier", default: false
    t.string "list_represents_model"
    t.index ["document_type_id"], name: "index_document_fields_on_document_type_id"
  end

  create_table "document_subfields", force: :cascade do |t|
    t.bigint "document_field_id", null: false
    t.string "name", null: false
    t.string "humanized_name"
    t.text "description"
    t.integer "format", default: 0
    t.boolean "displayed", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["document_field_id"], name: "index_document_subfields_on_document_field_id"
  end

  create_table "document_types", force: :cascade do |t|
    t.string "name"
    t.string "humanized_name"
    t.text "description"
    t.integer "target_type", default: 0
    t.string "regex"
    t.string "doc_format"
    t.integer "purpose", default: 0
    t.string "owner_type"
    t.bigint "owner_id"
    t.string "obsolescence_strategy_type"
    t.bigint "obsolescence_strategy_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "document_category", default: 0, null: false
    t.string "post_processor_name"
    t.index ["obsolescence_strategy_type", "obsolescence_strategy_id"], name: "idx_on_obsolescence_strategy_type_obsolescence_stra_cf78fb37e8"
    t.index ["obsolescence_strategy_type", "obsolescence_strategy_id"], name: "index_document_types_on_obsolescence_strategy"
    t.index ["owner_type", "owner_id"], name: "index_document_types_on_owner"
    t.index ["owner_type", "owner_id"], name: "index_document_types_on_owner_type_and_owner_id"
  end

  create_table "documents", force: :cascade do |t|
    t.bigint "upload_id"
    t.bigint "document_type_id"
    t.string "documentable_type"
    t.bigint "documentable_id"
    t.bigint "scope_id"
    t.bigint "reviewer_id"
    t.bigint "initial_document_id"
    t.integer "status", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["document_type_id"], name: "index_documents_on_document_type_id"
    t.index ["documentable_type", "documentable_id"], name: "index_documents_on_documentable"
    t.index ["initial_document_id"], name: "index_documents_on_initial_document_id"
    t.index ["reviewer_id"], name: "index_documents_on_reviewer_id"
    t.index ["scope_id"], name: "index_documents_on_scope_id"
    t.index ["upload_id"], name: "index_documents_on_upload_id"
  end

  create_table "equipment_donations", force: :cascade do |t|
    t.bigint "participant_id", null: false
    t.bigint "site_equipment_id", null: false
    t.integer "quantity", default: 1, null: false
    t.datetime "donation_date", default: -> { "CURRENT_TIMESTAMP" }, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["participant_id"], name: "index_equipment_donations_on_participant_id"
    t.index ["site_equipment_id"], name: "index_equipment_donations_on_site_equipment_id"
  end

  create_table "equipment_types", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "external_api_configurations", force: :cascade do |t|
    t.bigint "external_api_id", null: false
    t.integer "update_interval"
    t.string "base_url"
    t.datetime "last_update"
    t.bigint "api_user_id"
    t.jsonb "settings", default: {}
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "last_movements_fetch"
    t.index ["api_user_id"], name: "index_external_api_configurations_on_api_user_id"
    t.index ["external_api_id"], name: "index_external_api_configurations_on_external_api_id"
  end

  create_table "external_apis", force: :cascade do |t|
    t.string "service_name"
    t.integer "update_interval"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "external_badges", force: :cascade do |t|
    t.bigint "external_identifier_id", null: false
    t.string "badge_number", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["external_identifier_id"], name: "index_external_badges_on_external_identifier_id"
  end

  create_table "external_identifiers", force: :cascade do |t|
    t.bigint "participant_id", null: false
    t.string "external_id", null: false
    t.bigint "external_api_configuration_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["external_api_configuration_id"], name: "index_external_identifiers_on_external_api_configuration_id"
    t.index ["participant_id"], name: "index_external_identifiers_on_participant_id"
  end

  create_table "external_inspectors", force: :cascade do |t|
    t.bigint "company_id"
    t.string "email"
    t.string "name"
    t.string "token"
    t.datetime "token_expires_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_external_inspectors_on_company_id"
    t.index ["email"], name: "index_external_inspectors_on_email", unique: true
    t.index ["token"], name: "index_external_inspectors_on_token", unique: true
  end

  create_table "extracted_fields", force: :cascade do |t|
    t.bigint "document_id", null: false
    t.text "value"
    t.bigint "document_field_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["document_field_id"], name: "index_extracted_fields_on_document_field_id"
    t.index ["document_id"], name: "index_extracted_fields_on_document_id"
  end

  create_table "failed_condition_records", force: :cascade do |t|
    t.bigint "group_result_id", null: false
    t.string "element_type", null: false
    t.bigint "element_id", null: false
    t.string "message", null: false
    t.datetime "failed_at", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["element_type", "element_id", "failed_at"], name: "idx_on_element_type_element_id_failed_at_933fc38e0f"
    t.index ["element_type", "element_id"], name: "index_failed_condition_records_on_element"
    t.index ["group_result_id", "failed_at"], name: "idx_on_group_result_id_failed_at_f802e66bfc"
    t.index ["group_result_id"], name: "index_failed_condition_records_on_group_result_id"
  end

  create_table "feature_mappings", force: :cascade do |t|
    t.bigint "feature_id", null: false
    t.string "controllable_type", null: false
    t.bigint "controllable_id", null: false
    t.boolean "enabled", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["controllable_type", "controllable_id"], name: "index_feature_mappings_on_controllable"
    t.index ["feature_id", "controllable_type", "controllable_id"], name: "index_feature_mappings_uniqueness", unique: true
    t.index ["feature_id"], name: "index_feature_mappings_on_feature_id"
  end

  create_table "features", force: :cascade do |t|
    t.string "name", null: false
    t.string "humanized_name", null: false
    t.text "description", null: false
    t.integer "category", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["category"], name: "index_features_on_category"
    t.index ["name"], name: "index_features_on_name"
  end

  create_table "field_based_strategies", force: :cascade do |t|
    t.string "name", null: false
    t.string "field_name", null: false
    t.string "comparison_type", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "field_equals_parameters", force: :cascade do |t|
    t.bigint "document_type_id", null: false
    t.bigint "document_field_id", null: false
    t.string "expected_value", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["document_field_id"], name: "index_field_equals_parameters_on_document_field_id"
    t.index ["document_type_id"], name: "index_field_equals_parameters_on_document_type_id"
  end

  create_table "free_fields", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.bigint "moderated_by_id"
    t.text "value", null: false
    t.boolean "validated", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_free_fields_on_company_id"
    t.index ["moderated_by_id"], name: "index_free_fields_on_moderated_by_id"
  end

  create_table "gdpr_requests", force: :cascade do |t|
    t.string "company_person_type", null: false
    t.bigint "company_person_id", null: false
    t.string "action_type", null: false
    t.datetime "asked_at", null: false
    t.datetime "answered_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["company_person_type", "company_person_id"], name: "index_gdpr_requests_on_company_person"
  end

  create_table "group_results", force: :cascade do |t|
    t.bigint "condition_group_id", null: false
    t.string "subject_type", null: false
    t.bigint "subject_id", null: false
    t.date "valid_from"
    t.date "valid_until"
    t.boolean "is_valid"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["condition_group_id"], name: "index_group_results_on_condition_group_id"
    t.index ["subject_type", "subject_id"], name: "index_group_results_on_subject"
    t.index ["subject_type", "subject_id"], name: "index_group_results_on_subject_type_and_subject_id"
  end

  create_table "interim_accesses", force: :cascade do |t|
    t.bigint "interim_company_id", null: false
    t.string "accessible_type", null: false
    t.bigint "accessible_id", null: false
    t.boolean "is_active", default: true, null: false
    t.date "valid_from"
    t.date "valid_until"
    t.string "creator_type"
    t.bigint "creator_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["accessible_type", "accessible_id"], name: "index_interim_accesses_on_accessible"
    t.index ["creator_type", "creator_id"], name: "index_interim_accesses_on_creator"
    t.index ["interim_company_id", "accessible_type", "accessible_id"], name: "index_interim_accesses_uniqueness", unique: true
    t.index ["interim_company_id"], name: "index_interim_accesses_on_interim_company_id"
  end

  create_table "key_movements", force: :cascade do |t|
    t.integer "key_origin"
    t.integer "movement_type"
    t.datetime "movement_date"
    t.datetime "return_date"
    t.bigint "participant_id"
    t.bigint "returned_by_id"
    t.bigint "key_set_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["key_set_id"], name: "index_key_movements_on_key_set_id"
    t.index ["participant_id", "key_set_id"], name: "index_key_movements_on_participant_and_key_set"
    t.index ["participant_id"], name: "index_key_movements_on_participant_id"
    t.index ["returned_by_id"], name: "index_key_movements_on_returned_by_id"
  end

  create_table "key_sets", force: :cascade do |t|
    t.string "location"
    t.string "name"
    t.string "uuid"
    t.string "content"
    t.integer "status"
    t.string "creator_type"
    t.bigint "creator_id"
    t.bigint "site_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["creator_type", "creator_id"], name: "index_key_sets_on_creator"
    t.index ["location", "name"], name: "index_key_sets_on_location_and_name"
    t.index ["site_id"], name: "index_key_sets_on_site_id"
    t.index ["uuid"], name: "index_key_sets_on_uuid", unique: true
  end

  create_table "locker_damage_types", force: :cascade do |t|
    t.bigint "site_id", null: false
    t.string "name", null: false
    t.float "penalty_amount"
    t.string "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["site_id"], name: "index_locker_damage_types_on_site_id"
  end

  create_table "locker_damages", force: :cascade do |t|
    t.bigint "locker_id", null: false
    t.bigint "locker_participant_id"
    t.bigint "reported_by_id", null: false
    t.bigint "locker_damage_type_id", null: false
    t.text "description"
    t.datetime "damage_at"
    t.string "comment"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["locker_damage_type_id"], name: "index_locker_damages_on_locker_damage_type_id"
    t.index ["locker_id"], name: "index_locker_damages_on_locker_id"
    t.index ["locker_participant_id"], name: "index_locker_damages_on_locker_participant_id"
    t.index ["reported_by_id"], name: "index_locker_damages_on_reported_by_id"
  end

  create_table "locker_key_movements", force: :cascade do |t|
    t.bigint "locker_participant_id", null: false
    t.datetime "movement_at"
    t.integer "quantity", default: 1
    t.enum "movement_type", null: false, enum_type: "locker_key_movement_type"
    t.string "comment"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["locker_participant_id"], name: "index_locker_key_movements_on_locker_participant_id"
  end

  create_table "locker_participants", force: :cascade do |t|
    t.bigint "locker_id", null: false
    t.bigint "participant_id", null: false
    t.datetime "assigned_at"
    t.datetime "returned_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["locker_id", "participant_id"], name: "index_locker_participants_on_locker_id_and_participant_id", unique: true
    t.index ["locker_id"], name: "index_locker_participants_on_locker_id"
    t.index ["participant_id"], name: "index_locker_participants_on_participant_id"
  end

  create_table "lockers", force: :cascade do |t|
    t.bigint "site_id", null: false
    t.string "number", null: false
    t.string "zone", null: false
    t.integer "key_count", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "active_locker_participants_count", default: 0, null: false
    t.integer "locker_damages_count", default: 0, null: false
    t.index ["site_id", "number", "zone"], name: "index_lockers_on_site_id_and_number_and_zone", unique: true
    t.index ["site_id"], name: "index_lockers_on_site_id"
  end

  create_table "logical_nodes", force: :cascade do |t|
    t.integer "operator", null: false
    t.string "name", null: false
    t.string "left_element_type", null: false
    t.bigint "left_element_id", null: false
    t.string "right_element_type"
    t.bigint "right_element_id"
    t.string "fallback_message"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["left_element_type", "left_element_id"], name: "index_logical_nodes_on_left_element"
    t.index ["right_element_type", "right_element_id"], name: "index_logical_nodes_on_right_element"
  end

  create_table "lots", force: :cascade do |t|
    t.bigint "site_id", null: false
    t.string "number", null: false
    t.string "name", null: false
    t.string "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["site_id"], name: "index_lots_on_site_id"
  end

  create_table "participant_qualifications", force: :cascade do |t|
    t.bigint "participant_id", null: false
    t.bigint "site_qualification_requirement_id", null: false
    t.boolean "is_valid", default: false
    t.date "valid_from"
    t.date "valid_until"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["participant_id", "site_qualification_requirement_id"], name: "index_participant_qualifications_uniqueness", unique: true
    t.index ["participant_id"], name: "index_participant_qualifications_on_participant_id"
    t.index ["site_qualification_requirement_id"], name: "idx_on_site_qualification_requirement_id_2a15fb5451"
  end

  create_table "participant_role_sites", force: :cascade do |t|
    t.bigint "participant_role_id", null: false
    t.bigint "site_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["participant_role_id"], name: "index_participant_role_sites_on_participant_role_id"
    t.index ["site_id"], name: "index_participant_role_sites_on_site_id"
  end

  create_table "participant_roles", force: :cascade do |t|
    t.string "name"
    t.string "description"
    t.string "short_name"
    t.bigint "company_id"
    t.boolean "global", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "same_company", default: false
    t.index ["company_id"], name: "index_participant_roles_on_company_id"
  end

  create_table "participants", force: :cascade do |t|
    t.bigint "person_id", null: false
    t.bigint "assignment_id", null: false
    t.string "creator_type"
    t.bigint "creator_id"
    t.bigint "participant_role_site_id"
    t.boolean "is_valid", default: false, null: false
    t.date "valid_from"
    t.date "valid_until"
    t.string "qr_code"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["assignment_id"], name: "index_participants_on_assignment_id"
    t.index ["creator_type", "creator_id"], name: "index_participants_on_creator"
    t.index ["participant_role_site_id"], name: "index_participants_on_participant_role_site_id"
    t.index ["person_id"], name: "index_participants_on_person_id"
  end

  create_table "penalty_accounts", force: :cascade do |t|
    t.bigint "person_id", null: false
    t.bigint "site_id", null: false
    t.integer "points_remaining", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["person_id"], name: "index_penalty_accounts_on_person_id"
    t.index ["site_id"], name: "index_penalty_accounts_on_site_id"
  end

  create_table "pending_participants", force: :cascade do |t|
    t.string "first_name"
    t.string "last_name"
    t.date "birth_date"
    t.boolean "is_interim", default: false
    t.bigint "company_id"
    t.bigint "assignment_id"
    t.bigint "site_id", null: false
    t.bigint "participant_role_site_id"
    t.boolean "is_valid", default: false
    t.datetime "converted_at"
    t.integer "status", default: 0, null: false
    t.bigint "participant_id"
    t.date "valid_from"
    t.date "valid_until"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "user_id"
    t.index ["assignment_id"], name: "index_pending_participants_on_assignment_id"
    t.index ["company_id"], name: "index_pending_participants_on_company_id"
    t.index ["converted_at"], name: "index_pending_participants_on_converted_at"
    t.index ["participant_id"], name: "index_pending_participants_on_participant_id"
    t.index ["participant_role_site_id"], name: "index_pending_participants_on_participant_role_site_id"
    t.index ["site_id"], name: "index_pending_participants_on_site_id"
    t.index ["status"], name: "index_pending_participants_on_status"
    t.index ["user_id"], name: "index_pending_participants_on_user_id"
  end

  create_table "people", force: :cascade do |t|
    t.string "first_name"
    t.string "last_name"
    t.bigint "company_id", null: false
    t.date "birth_date"
    t.string "creator_type"
    t.bigint "creator_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_people_on_company_id"
    t.index ["creator_type", "creator_id"], name: "index_people_on_creator"
  end

  create_table "permissions", force: :cascade do |t|
    t.string "name", null: false
    t.string "key", null: false
    t.text "description"
    t.string "category"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["category"], name: "index_permissions_on_category"
    t.index ["key"], name: "index_permissions_on_key", unique: true
  end

  create_table "person_qualifications", force: :cascade do |t|
    t.bigint "qualification_catalog_id", null: false
    t.bigint "company_person_id", null: false
    t.date "valid_from"
    t.date "valid_until"
    t.boolean "is_valid", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["company_person_id", "qualification_catalog_id"], name: "idx_on_company_person_id_qualification_catalog_id_e57abe6450", unique: true
    t.index ["company_person_id"], name: "index_person_qualifications_on_company_person_id"
    t.index ["qualification_catalog_id"], name: "index_person_qualifications_on_qualification_catalog_id"
  end

  create_table "presence_details", force: :cascade do |t|
    t.bigint "presence_id", null: false
    t.bigint "participant_role_site_id", null: false
    t.integer "count"
    t.integer "hours"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["participant_role_site_id"], name: "index_presence_details_on_participant_role_site_id"
    t.index ["presence_id"], name: "index_presence_details_on_presence_id"
  end

  create_table "presence_movements", force: :cascade do |t|
    t.integer "movement_type"
    t.datetime "movement_at"
    t.bigint "participant_id", null: false
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "gate_status", default: false
    t.index ["participant_id"], name: "index_presence_movements_on_participant_id"
    t.index ["user_id"], name: "index_presence_movements_on_user_id"
  end

  create_table "presence_parameters", force: :cascade do |t|
    t.bigint "document_type_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["document_type_id"], name: "index_presence_parameters_on_document_type_id"
  end

  create_table "presence_upload_logs", force: :cascade do |t|
    t.bigint "site_id", null: false
    t.string "feedback_code", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["site_id"], name: "index_presence_upload_logs_on_site_id"
  end

  create_table "presences", force: :cascade do |t|
    t.bigint "assignment_id"
    t.date "date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["assignment_id"], name: "index_presences_on_assignment_id"
  end

  create_table "qualification_catalogs", force: :cascade do |t|
    t.string "name", null: false
    t.text "description"
    t.string "short_name", null: false
    t.bigint "company_id"
    t.bigint "condition_group_id", null: false
    t.boolean "global", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_qualification_catalogs_on_company_id"
    t.index ["condition_group_id"], name: "index_qualification_catalogs_on_condition_group_id"
  end

  create_table "replace_all_strategies", force: :cascade do |t|
    t.string "name", default: "Remplacer tous les documents précédents", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "replace_if_newer_strategies", force: :cascade do |t|
    t.string "name", default: "Remplacer les documents plus anciens", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "role_contexts", force: :cascade do |t|
    t.bigint "assignment_role_site_id"
    t.bigint "participant_role_site_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["assignment_role_site_id"], name: "index_role_contexts_on_assignment_role_site_id"
    t.index ["participant_role_site_id"], name: "index_role_contexts_on_participant_role_site_id"
  end

  create_table "safety_session_signatures", force: :cascade do |t|
    t.bigint "safety_session_id", null: false
    t.bigint "participant_id", null: false
    t.datetime "signed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["participant_id"], name: "index_safety_session_signatures_on_participant_id"
    t.index ["safety_session_id"], name: "index_safety_session_signatures_on_safety_session_id"
  end

  create_table "safety_sessions", force: :cascade do |t|
    t.bigint "site_id", null: false
    t.bigint "safety_theme_id", null: false
    t.string "uuid"
    t.datetime "session_date"
    t.integer "participants_count", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["safety_theme_id"], name: "index_safety_sessions_on_safety_theme_id"
    t.index ["site_id"], name: "index_safety_sessions_on_site_id"
    t.index ["uuid"], name: "index_safety_sessions_on_uuid", unique: true
  end

  create_table "safety_themes", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "sanction_types", force: :cascade do |t|
    t.string "name", null: false
    t.text "description"
    t.integer "default_points", default: 1, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_sanction_types_on_name", unique: true
  end

  create_table "sanctions", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "sanction_type_id", null: false
    t.integer "points_sanctioned", default: 1
    t.text "comment"
    t.bigint "penalty_account_id", null: false
    t.bigint "assignment_id", null: false
    t.datetime "sanction_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["assignment_id"], name: "index_sanctions_on_assignment_id"
    t.index ["penalty_account_id"], name: "index_sanctions_on_penalty_account_id"
    t.index ["sanction_type_id"], name: "index_sanctions_on_sanction_type_id"
    t.index ["user_id"], name: "index_sanctions_on_user_id"
  end

  create_table "scopes", force: :cascade do |t|
    t.bigint "site_id", null: false
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["site_id"], name: "index_scopes_on_site_id"
  end

  create_table "setting_mappings", force: :cascade do |t|
    t.bigint "setting_id", null: false
    t.string "controllable_type", null: false
    t.bigint "controllable_id", null: false
    t.string "value"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "locked", default: false
    t.index ["controllable_type", "controllable_id"], name: "index_setting_mappings_on_controllable"
    t.index ["setting_id", "controllable_type", "controllable_id"], name: "index_setting_mappings_uniqueness", unique: true
    t.index ["setting_id"], name: "index_setting_mappings_on_setting_id"
  end

  create_table "settings", force: :cascade do |t|
    t.string "name", null: false
    t.string "default_value"
    t.text "description"
    t.bigint "feature_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "value_type"
    t.index ["feature_id"], name: "index_settings_on_feature_id"
    t.index ["name"], name: "index_settings_on_name"
  end

  create_table "signable_templates", force: :cascade do |t|
    t.bigint "document_type_id", null: false
    t.string "owner_type"
    t.bigint "owner_id"
    t.string "title", null: false
    t.text "content", null: false
    t.integer "version", default: 1, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["document_type_id", "version", "owner_type", "owner_id"], name: "index_signable_templates_uniqueness", unique: true
    t.index ["document_type_id"], name: "index_signable_templates_on_document_type_id"
    t.index ["owner_type", "owner_id"], name: "index_signable_templates_on_owner"
  end

  create_table "site_configuration_templates", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.boolean "is_default"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["is_default"], name: "index_site_configuration_templates_on_is_default"
  end

  create_table "site_control_entries", force: :cascade do |t|
    t.bigint "site_control_id", null: false
    t.bigint "participant_id", null: false
    t.string "status"
    t.datetime "checked_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["participant_id"], name: "index_site_control_entries_on_participant_id"
    t.index ["site_control_id"], name: "index_site_control_entries_on_site_control_id"
  end

  create_table "site_control_incidents", force: :cascade do |t|
    t.bigint "site_control_id"
    t.bigint "site_id", null: false
    t.bigint "reported_by_id"
    t.bigint "participant_id"
    t.bigint "unknown_participant_id"
    t.jsonb "details"
    t.text "description"
    t.bigint "resolved_by_id"
    t.datetime "resolved_at"
    t.string "incident_type", default: "authorization"
    t.string "status", default: "pending"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["participant_id"], name: "index_site_control_incidents_on_participant_id"
    t.index ["reported_by_id"], name: "index_site_control_incidents_on_reported_by_id"
    t.index ["resolved_by_id"], name: "index_site_control_incidents_on_resolved_by_id"
    t.index ["site_control_id"], name: "index_site_control_incidents_on_site_control_id"
    t.index ["site_id"], name: "index_site_control_incidents_on_site_id"
    t.index ["unknown_participant_id"], name: "index_site_control_incidents_on_unknown_participant_id"
  end

  create_table "site_controls", force: :cascade do |t|
    t.bigint "site_id", null: false
    t.string "inspector_type"
    t.bigint "inspector_id"
    t.datetime "control_date"
    t.datetime "scheduled_at"
    t.string "status"
    t.text "comments"
    t.bigint "created_by_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "site_control_entries_count", default: 0
    t.integer "site_control_incidents_count", default: 0
    t.integer "site_control_unknown_incidents_count", default: 0
    t.index ["created_by_id"], name: "index_site_controls_on_created_by_id"
    t.index ["inspector_type", "inspector_id"], name: "index_site_controls_on_inspector_type_and_inspector_id"
    t.index ["site_id"], name: "index_site_controls_on_site_id"
    t.index ["status"], name: "index_site_controls_on_status"
  end

  create_table "site_details", force: :cascade do |t|
    t.bigint "site_id", null: false
    t.integer "floor_area"
    t.string "permit_number"
    t.text "description"
    t.string "site_image"
    t.date "permit_date"
    t.string "base_email"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["site_id"], name: "index_site_details_on_site_id"
  end

  create_table "site_equipments", force: :cascade do |t|
    t.bigint "site_id", null: false
    t.bigint "equipment_type_id", null: false
    t.string "size"
    t.string "reference"
    t.integer "stock_quantity"
    t.decimal "purchase_price"
    t.decimal "sale_price"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["equipment_type_id"], name: "index_site_equipments_on_equipment_type_id"
    t.index ["site_id"], name: "index_site_equipments_on_site_id"
  end

  create_table "site_meetings", force: :cascade do |t|
    t.datetime "start_date"
    t.datetime "end_date"
    t.string "place"
    t.string "title"
    t.string "description"
    t.boolean "is_private", default: false
    t.string "ics_uid"
    t.integer "attendees_count", default: 0
    t.bigint "site_id", null: false
    t.bigint "created_by_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_by_id"], name: "index_site_meetings_on_created_by_id"
    t.index ["site_id"], name: "index_site_meetings_on_site_id"
  end

  create_table "site_point_quota", force: :cascade do |t|
    t.bigint "site_id", null: false
    t.integer "point_quota", default: 3
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["site_id"], name: "index_site_point_quota_on_site_id"
  end

  create_table "site_qualification_requirements", force: :cascade do |t|
    t.bigint "site_id", null: false
    t.bigint "qualification_catalog_id", null: false
    t.bigint "condition_group_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["condition_group_id"], name: "index_site_qualification_requirements_on_condition_group_id"
    t.index ["qualification_catalog_id"], name: "idx_on_qualification_catalog_id_b257f66909"
    t.index ["site_id", "qualification_catalog_id"], name: "index_site_qualification_requirements_uniqueness", unique: true
    t.index ["site_id"], name: "index_site_qualification_requirements_on_site_id"
  end

  create_table "site_role_condition_groups", force: :cascade do |t|
    t.bigint "condition_group_id", null: false
    t.string "conditionable_type", null: false
    t.bigint "conditionable_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["condition_group_id"], name: "index_site_role_condition_groups_on_condition_group_id"
    t.index ["conditionable_type", "conditionable_id"], name: "index_site_role_condition_groups_on_conditionable"
  end

  create_table "sites", force: :cascade do |t|
    t.string "name"
    t.string "short_name"
    t.string "address"
    t.bigint "company_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_sites_on_company_id"
  end

  create_table "stakeholders", force: :cascade do |t|
    t.bigint "construction_board_id", null: false
    t.string "name", null: false
    t.string "role"
    t.string "logo"
    t.jsonb "other_infos"
    t.string "address"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["construction_board_id"], name: "index_stakeholders_on_construction_board_id"
  end

  create_table "tasks", force: :cascade do |t|
    t.string "title", null: false
    t.text "description"
    t.integer "status", default: 0, null: false
    t.date "due_date"
    t.string "assignee_type"
    t.bigint "assignee_id"
    t.bigint "assigner_id", null: false
    t.bigint "site_id", null: false
    t.string "task_type", default: "guardian", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["assignee_type", "assignee_id"], name: "index_tasks_on_assignee"
    t.index ["assigner_id"], name: "index_tasks_on_assigner_id"
    t.index ["due_date"], name: "index_tasks_on_due_date"
    t.index ["site_id"], name: "index_tasks_on_site_id"
    t.index ["status"], name: "index_tasks_on_status"
    t.index ["task_type"], name: "index_tasks_on_task_type"
  end

  create_table "template_assignment_roles", force: :cascade do |t|
    t.bigint "site_configuration_template_id", null: false
    t.bigint "assignment_role_id", null: false
    t.string "condition_group_short_names", default: [], array: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["assignment_role_id"], name: "index_template_assignment_roles_on_assignment_role_id"
    t.index ["site_configuration_template_id"], name: "idx_on_site_configuration_template_id_b89141d908"
  end

  create_table "template_participant_roles", force: :cascade do |t|
    t.bigint "site_configuration_template_id", null: false
    t.bigint "participant_role_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["participant_role_id"], name: "index_template_participant_roles_on_participant_role_id"
    t.index ["site_configuration_template_id"], name: "idx_on_site_configuration_template_id_0e319536cb"
  end

  create_table "template_role_contexts", force: :cascade do |t|
    t.bigint "site_configuration_template_id", null: false
    t.bigint "assignment_role_id"
    t.bigint "participant_role_id"
    t.string "condition_group_short_names", default: [], array: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["assignment_role_id"], name: "index_template_role_contexts_on_assignment_role_id"
    t.index ["participant_role_id"], name: "index_template_role_contexts_on_participant_role_id"
    t.index ["site_configuration_template_id"], name: "index_template_role_contexts_on_site_configuration_template_id"
  end

  create_table "unknown_participants", force: :cascade do |t|
    t.string "first_name"
    t.string "last_name"
    t.string "company"
    t.text "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "upload_issues", force: :cascade do |t|
    t.bigint "upload_id", null: false
    t.string "filename"
    t.string "error_type"
    t.text "details"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["upload_id"], name: "index_upload_issues_on_upload_id"
  end

  create_table "upload_logs", force: :cascade do |t|
    t.integer "status"
    t.jsonb "message"
    t.bigint "upload_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["upload_id"], name: "index_upload_logs_on_upload_id"
  end

  create_table "uploads", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "site_id"
    t.integer "status", default: 0
    t.integer "import_method", default: 0
    t.boolean "admin_review", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "perimeterable_type"
    t.bigint "perimeterable_id"
    t.index ["perimeterable_type", "perimeterable_id"], name: "index_uploads_on_perimeterable"
    t.index ["site_id"], name: "index_uploads_on_site_id"
    t.index ["user_id"], name: "index_uploads_on_user_id"
  end

  create_table "user_role_permissions", force: :cascade do |t|
    t.bigint "permission_id", null: false
    t.string "permissible_type", null: false
    t.bigint "permissible_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["permissible_type", "permissible_id"], name: "index_user_role_permissions_on_permissible"
    t.index ["permission_id", "permissible_id", "permissible_type"], name: "index_user_role_permissions_uniqueness", unique: true
    t.index ["permission_id"], name: "index_user_role_permissions_on_permission_id"
  end

  create_table "user_role_sites", force: :cascade do |t|
    t.bigint "user_site_id", null: false
    t.bigint "user_role_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_role_id"], name: "index_user_role_sites_on_user_role_id"
    t.index ["user_site_id", "user_role_id"], name: "index_user_role_sites_on_user_site_id_and_user_role_id", unique: true
    t.index ["user_site_id"], name: "index_user_role_sites_on_user_site_id"
  end

  create_table "user_roles", force: :cascade do |t|
    t.string "name", null: false
    t.string "key", null: false
    t.text "description"
    t.string "perimeter_type"
    t.bigint "perimeter_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["key", "perimeter_type", "perimeter_id"], name: "index_user_roles_on_key_and_perimeter_type_and_perimeter_id", unique: true
    t.index ["perimeter_type", "perimeter_id"], name: "index_user_roles_on_perimeter"
  end

  create_table "user_settings", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.integer "session_timeout_seconds"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_user_settings_on_user_id", unique: true
  end

  create_table "user_sites", force: :cascade do |t|
    t.bigint "site_id", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["site_id"], name: "index_user_sites_on_site_id"
    t.index ["user_id"], name: "index_user_sites_on_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.integer "last_user_site_id"
    t.datetime "remember_created_at"
    t.string "invitation_token"
    t.datetime "invitation_created_at"
    t.datetime "invitation_sent_at"
    t.datetime "invitation_accepted_at"
    t.integer "invitation_limit"
    t.string "invited_by_type"
    t.bigint "invited_by_id"
    t.integer "invitations_count", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "company_id", null: false
    t.string "first_name"
    t.string "last_name"
    t.string "phone_number"
    t.boolean "admin", default: false
    t.string "otp_secret"
    t.datetime "last_otp_at"
    t.boolean "otp_verified", default: false
    t.integer "otp_attempts", default: 0, null: false
    t.index ["company_id"], name: "index_users_on_company_id"
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["invitation_token"], name: "index_users_on_invitation_token", unique: true
    t.index ["invited_by_id"], name: "index_users_on_invited_by_id"
    t.index ["invited_by_type", "invited_by_id"], name: "index_users_on_invited_by"
    t.index ["otp_attempts"], name: "index_users_on_otp_attempts"
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
  end

  create_table "validations", force: :cascade do |t|
    t.string "validable_type", null: false
    t.bigint "validable_id", null: false
    t.string "validator_type", null: false
    t.bigint "validator_id", null: false
    t.string "status", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["validable_type", "validable_id"], name: "index_validations_on_validable"
    t.index ["validator_type", "validator_id"], name: "index_validations_on_validator"
  end

  create_table "validity_inherits_parameters", force: :cascade do |t|
    t.string "source_relation"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "versions", force: :cascade do |t|
    t.string "whodunnit"
    t.datetime "created_at"
    t.bigint "item_id", null: false
    t.string "item_type", null: false
    t.string "event", null: false
    t.jsonb "object"
    t.index ["item_type", "item_id"], name: "index_versions_on_item_type_and_item_id"
  end

  create_table "vetting_cross_fields_parameters", force: :cascade do |t|
    t.bigint "source_document_type_id", null: false
    t.bigint "source_document_field_id", null: false
    t.bigint "target_document_type_id", null: false
    t.bigint "target_document_field_id", null: false
    t.integer "operator", null: false
    t.float "threshold"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "vetting_custom_parameters", force: :cascade do |t|
    t.string "checker_class", null: false
    t.jsonb "parameters", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "vetting_field_integrity_parameters", force: :cascade do |t|
    t.bigint "document_type_id", null: false
    t.bigint "document_field_id", null: false
    t.integer "operator", null: false
    t.string "expected_value"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "vetting_object_attribute_parameters", force: :cascade do |t|
    t.bigint "document_type_id", null: false
    t.bigint "document_field_id", null: false
    t.string "object_type", null: false
    t.string "object_relation_path", null: false
    t.string "attribute_name", null: false
    t.integer "operator", null: false
    t.float "threshold"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["document_field_id"], name: "index_vetting_object_attribute_parameters_on_document_field_id"
    t.index ["document_type_id"], name: "index_vetting_object_attribute_parameters_on_document_type_id"
  end

  create_table "vetting_processes", force: :cascade do |t|
    t.string "vettable_type", null: false
    t.bigint "vettable_id", null: false
    t.string "validator_type"
    t.bigint "validator_id"
    t.integer "status", default: 0
    t.boolean "is_favorable", default: false
    t.datetime "validated_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["validator_type", "validator_id"], name: "index_vetting_processes_on_validator"
    t.index ["vettable_type", "vettable_id"], name: "index_vetting_processes_on_vettable"
  end

  create_table "vetting_rule_mappings", force: :cascade do |t|
    t.bigint "vetting_rule_id", null: false
    t.string "controllable_type", null: false
    t.bigint "controllable_id", null: false
    t.boolean "is_enabled", null: false
    t.boolean "locked", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["controllable_type", "controllable_id"], name: "index_vetting_rule_mappings_on_controllable"
    t.index ["vetting_rule_id", "controllable_id", "controllable_type"], name: "index_vetting_rule_mappings_unique", unique: true
    t.index ["vetting_rule_id"], name: "index_vetting_rule_mappings_on_vetting_rule_id"
  end

  create_table "vetting_rule_results", force: :cascade do |t|
    t.bigint "vetting_process_id", null: false
    t.string "parameter_type", null: false
    t.bigint "parameter_id", null: false
    t.text "human_details"
    t.integer "result", null: false
    t.jsonb "details"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["parameter_type", "parameter_id"], name: "index_vetting_rule_results_on_parameter"
    t.index ["vetting_process_id"], name: "index_vetting_rule_results_on_vetting_process_id"
  end

  create_table "vetting_rules", force: :cascade do |t|
    t.string "name", null: false
    t.string "code", null: false
    t.text "description"
    t.integer "applicable_to", default: 0
    t.string "parameter_type"
    t.bigint "parameter_id"
    t.bigint "site_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["code"], name: "index_vetting_rules_on_code", unique: true
    t.index ["parameter_type", "parameter_id"], name: "index_vetting_rules_on_parameter"
    t.index ["site_id"], name: "index_vetting_rules_on_site_id"
  end

  create_table "visual_badge_requests", force: :cascade do |t|
    t.bigint "participant_id", null: false
    t.string "requested_by_type", null: false
    t.bigint "requested_by_id", null: false
    t.datetime "generated_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["participant_id"], name: "index_visual_badge_requests_on_participant_id"
    t.index ["requested_by_type", "requested_by_id"], name: "index_visual_badge_requests_on_requested_by"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "api_permissions", "api_users"
  add_foreign_key "assignment_lots", "assignments"
  add_foreign_key "assignment_lots", "lots"
  add_foreign_key "assignment_role_sites", "assignment_roles"
  add_foreign_key "assignment_role_sites", "sites"
  add_foreign_key "assignment_roles", "assignment_role_categories"
  add_foreign_key "assignment_roles", "companies"
  add_foreign_key "assignments", "assignment_role_sites"
  add_foreign_key "assignments", "assignments", column: "assignment_parent_id"
  add_foreign_key "assignments", "companies"
  add_foreign_key "assignments", "sites"
  add_foreign_key "assignments_referents", "assignments"
  add_foreign_key "assignments_referents", "user_sites"
  add_foreign_key "association_exists_parameters", "document_types"
  add_foreign_key "attendees", "site_meetings"
  add_foreign_key "boolean_parameters", "document_fields"
  add_foreign_key "boolean_parameters", "document_types"
  add_foreign_key "bypasses", "users", column: "created_by_id"
  add_foreign_key "company_connections", "companies", column: "source_company_id"
  add_foreign_key "company_connections", "companies", column: "target_company_id"
  add_foreign_key "company_people", "companies"
  add_foreign_key "company_people", "people"
  add_foreign_key "company_settings", "companies"
  add_foreign_key "condition_group_mappings", "condition_groups"
  add_foreign_key "condition_groups", "companies"
  add_foreign_key "conditions", "companies"
  add_foreign_key "construction_boards", "sites"
  add_foreign_key "contacts", "assignments"
  add_foreign_key "custom_parameters", "document_types"
  add_foreign_key "date_parameters", "document_fields"
  add_foreign_key "date_parameters", "document_types"
  add_foreign_key "default_user_assignment_roles", "assignment_roles"
  add_foreign_key "default_user_assignment_roles", "user_roles"
  add_foreign_key "document_associations", "documents"
  add_foreign_key "document_field_options", "document_fields"
  add_foreign_key "document_fields", "document_types"
  add_foreign_key "document_subfields", "document_fields"
  add_foreign_key "documents", "document_types"
  add_foreign_key "documents", "documents", column: "initial_document_id"
  add_foreign_key "documents", "scopes"
  add_foreign_key "documents", "uploads"
  add_foreign_key "documents", "users", column: "reviewer_id"
  add_foreign_key "equipment_donations", "participants"
  add_foreign_key "equipment_donations", "site_equipments"
  add_foreign_key "external_api_configurations", "api_users"
  add_foreign_key "external_api_configurations", "external_apis"
  add_foreign_key "external_badges", "external_identifiers"
  add_foreign_key "external_identifiers", "external_api_configurations"
  add_foreign_key "external_identifiers", "participants"
  add_foreign_key "external_inspectors", "companies"
  add_foreign_key "extracted_fields", "document_fields"
  add_foreign_key "extracted_fields", "documents"
  add_foreign_key "failed_condition_records", "group_results"
  add_foreign_key "feature_mappings", "features"
  add_foreign_key "field_equals_parameters", "document_fields"
  add_foreign_key "field_equals_parameters", "document_types"
  add_foreign_key "free_fields", "companies"
  add_foreign_key "free_fields", "users", column: "moderated_by_id"
  add_foreign_key "group_results", "condition_groups"
  add_foreign_key "interim_accesses", "companies", column: "interim_company_id"
  add_foreign_key "key_movements", "key_sets"
  add_foreign_key "key_movements", "participants"
  add_foreign_key "key_movements", "participants", column: "returned_by_id"
  add_foreign_key "key_sets", "sites"
  add_foreign_key "locker_damage_types", "sites"
  add_foreign_key "locker_damages", "locker_damage_types"
  add_foreign_key "locker_damages", "locker_participants"
  add_foreign_key "locker_damages", "lockers"
  add_foreign_key "locker_damages", "users", column: "reported_by_id"
  add_foreign_key "locker_key_movements", "locker_participants"
  add_foreign_key "locker_participants", "lockers"
  add_foreign_key "locker_participants", "participants"
  add_foreign_key "lockers", "sites"
  add_foreign_key "lots", "sites"
  add_foreign_key "participant_qualifications", "participants"
  add_foreign_key "participant_qualifications", "site_qualification_requirements"
  add_foreign_key "participant_role_sites", "participant_roles"
  add_foreign_key "participant_role_sites", "sites"
  add_foreign_key "participant_roles", "companies"
  add_foreign_key "participants", "assignments"
  add_foreign_key "participants", "participant_role_sites"
  add_foreign_key "participants", "people"
  add_foreign_key "penalty_accounts", "people"
  add_foreign_key "penalty_accounts", "sites"
  add_foreign_key "pending_participants", "assignments"
  add_foreign_key "pending_participants", "companies"
  add_foreign_key "pending_participants", "participant_role_sites"
  add_foreign_key "pending_participants", "participants"
  add_foreign_key "pending_participants", "sites"
  add_foreign_key "pending_participants", "users"
  add_foreign_key "people", "companies"
  add_foreign_key "person_qualifications", "company_people"
  add_foreign_key "person_qualifications", "qualification_catalogs"
  add_foreign_key "presence_details", "participant_role_sites"
  add_foreign_key "presence_details", "presences"
  add_foreign_key "presence_movements", "participants"
  add_foreign_key "presence_movements", "users"
  add_foreign_key "presence_parameters", "document_types"
  add_foreign_key "presence_upload_logs", "sites"
  add_foreign_key "presences", "assignments"
  add_foreign_key "qualification_catalogs", "companies"
  add_foreign_key "qualification_catalogs", "condition_groups"
  add_foreign_key "role_contexts", "assignment_role_sites"
  add_foreign_key "role_contexts", "participant_role_sites"
  add_foreign_key "safety_session_signatures", "participants"
  add_foreign_key "safety_session_signatures", "safety_sessions"
  add_foreign_key "safety_sessions", "safety_themes"
  add_foreign_key "safety_sessions", "sites"
  add_foreign_key "sanctions", "assignments"
  add_foreign_key "sanctions", "penalty_accounts"
  add_foreign_key "sanctions", "sanction_types"
  add_foreign_key "sanctions", "users"
  add_foreign_key "scopes", "sites"
  add_foreign_key "setting_mappings", "settings"
  add_foreign_key "settings", "features"
  add_foreign_key "signable_templates", "document_types"
  add_foreign_key "site_control_entries", "participants"
  add_foreign_key "site_control_entries", "site_controls"
  add_foreign_key "site_control_incidents", "participants"
  add_foreign_key "site_control_incidents", "site_controls"
  add_foreign_key "site_control_incidents", "sites"
  add_foreign_key "site_control_incidents", "unknown_participants"
  add_foreign_key "site_control_incidents", "users", column: "reported_by_id"
  add_foreign_key "site_control_incidents", "users", column: "resolved_by_id"
  add_foreign_key "site_controls", "sites"
  add_foreign_key "site_controls", "users", column: "created_by_id"
  add_foreign_key "site_details", "sites"
  add_foreign_key "site_equipments", "equipment_types"
  add_foreign_key "site_equipments", "sites"
  add_foreign_key "site_meetings", "sites"
  add_foreign_key "site_meetings", "users", column: "created_by_id"
  add_foreign_key "site_point_quota", "sites"
  add_foreign_key "site_qualification_requirements", "condition_groups"
  add_foreign_key "site_qualification_requirements", "qualification_catalogs"
  add_foreign_key "site_qualification_requirements", "sites"
  add_foreign_key "site_role_condition_groups", "condition_groups"
  add_foreign_key "sites", "companies"
  add_foreign_key "stakeholders", "construction_boards"
  add_foreign_key "tasks", "sites"
  add_foreign_key "tasks", "users", column: "assigner_id"
  add_foreign_key "template_assignment_roles", "assignment_roles"
  add_foreign_key "template_assignment_roles", "site_configuration_templates"
  add_foreign_key "template_participant_roles", "participant_roles"
  add_foreign_key "template_participant_roles", "site_configuration_templates"
  add_foreign_key "template_role_contexts", "assignment_roles"
  add_foreign_key "template_role_contexts", "participant_roles"
  add_foreign_key "template_role_contexts", "site_configuration_templates"
  add_foreign_key "upload_issues", "uploads"
  add_foreign_key "upload_logs", "uploads"
  add_foreign_key "uploads", "sites"
  add_foreign_key "uploads", "users"
  add_foreign_key "user_role_permissions", "permissions"
  add_foreign_key "user_role_sites", "user_roles"
  add_foreign_key "user_role_sites", "user_sites"
  add_foreign_key "user_settings", "users"
  add_foreign_key "user_sites", "sites"
  add_foreign_key "user_sites", "users"
  add_foreign_key "users", "companies"
  add_foreign_key "visual_badge_requests", "participants"
end
