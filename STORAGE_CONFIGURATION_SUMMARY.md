# Storage Configuration Implementation Summary

## Overview
This implementation adds Rails.env.demo? support and configures environment-specific storage configurations with a clean, maintainable approach.

## Changes Made

### 1. Rails Environment Extensions
- **File**: `config/initializers/rails_env_extensions.rb`
- **Purpose**: Adds `Rails.env.demo?` and `Rails.env.sandbox?` methods
- **Implementation**: Extends Rails::Env class with custom environment detection methods

### 2. Storage Helper
- **File**: `app/helpers/storage_helper.rb`
- **Purpose**: Centralizes storage service selection logic
- **Key Features**:
  - `storage_service_for_environment`: Returns appropriate storage service based on environment
  - `uploads_service`: Returns uploads service (always `:outscale_uploads`)
  - `documents_service`: Returns documents service (always `:outscale_documents`)
  - Sandbox and demo environments share the same bucket (`:outscale_storage_sandbox`)

### 3. Storage Configuration Updates
- **File**: `config/storage.yml`
- **Changes**:
  - Added environment variable support for bucket names
  - Added `outscale_storage_development` configuration
  - Added `outscale_storage_demo` configuration
  - Demo environment shares the same bucket as sandbox
  - Fallback credentials for sandbox/demo environments

### 4. Model Updates
Updated all models with `has_one_attached` and `has_many_attached` to use the StorageHelper:

#### Models using `StorageHelper.storage_service_for_environment`:
- `Company#logo`
- `Participant#photo`
- `PendingParticipant#photo`
- `KeySet#photo`
- `Site#locker_plan`
- `ConstructionBoard#images`
- `Sanction#image`
- `Stakeholder#logo`
- `SiteDetail#site_image`
- `CompanyPerson#photo`

#### Models using specific services:
- `Document#file` → `StorageHelper.documents_service`
- `Upload#file` → `StorageHelper.uploads_service`

### 5. Environment Configuration
- **Demo environment**: Already configured to use `:outscale_storage_demo`
- **Development environment**: Uses `:outscale_storage_development`
- **Sandbox environment**: Uses `:outscale_storage_sandbox`
- **Production environment**: Uses `:outscale_storage`

## Environment Variable Configuration

### Required Environment Variables:
```bash
# Base credentials (used as fallback)
OUTSCALE_ACCESS_KEY_ID=your_access_key
OUTSCALE_SECRET_ACCESS_KEY=your_secret_key

# Environment-specific credentials (optional, falls back to base)
OUTSCALE_ACCESS_KEY_ID_DEVELOPMENT=dev_access_key
OUTSCALE_SECRET_ACCESS_KEY_DEVELOPMENT=dev_secret_key
OUTSCALE_ACCESS_KEY_ID_SANDBOX=sandbox_access_key
OUTSCALE_SECRET_ACCESS_KEY_SANDBOX=sandbox_secret_key
OUTSCALE_ACCESS_KEY_ID_DEMO=demo_access_key
OUTSCALE_SECRET_ACCESS_KEY_DEMO=demo_secret_key

# Bucket names (optional, with sensible defaults)
OUTSCALE_STORAGE_BUCKET=mashe-storage
OUTSCALE_STORAGE_DEVELOPMENT_BUCKET=mashe-storage-development
OUTSCALE_STORAGE_SANDBOX_BUCKET=mashe-storage-sandbox
OUTSCALE_STORAGE_DEMO_BUCKET=mashe-storage-sandbox  # Shares with sandbox
OUTSCALE_UPLOADS_BUCKET=mashe-fold-uploads
OUTSCALE_DOCUMENTS_BUCKET=mashe-fold-documents
```

## Key Benefits

1. **Centralized Logic**: All storage service selection is handled in one place
2. **Environment Flexibility**: Easy to add new environments or change bucket configurations
3. **Shared Resources**: Sandbox and demo environments share the same bucket as requested
4. **Environment Variables**: Bucket names are configurable via environment variables
5. **Fallback Support**: Graceful fallbacks for missing environment-specific credentials
6. **Clean Code**: Removed repetitive conditional logic from models

## Testing

The implementation has been tested with a mock script (`test_storage_config.rb`) that verifies:
- `Rails.env.demo?` returns `true` only in demo environment
- `Rails.env.sandbox?` returns `true` only in sandbox environment
- Sandbox and demo environments both use `:outscale_storage_sandbox`
- Each environment gets the correct storage service

## Usage Examples

```ruby
# Check environment
Rails.env.demo?     # true in demo environment
Rails.env.sandbox?  # true in sandbox environment

# Get storage service for current environment
StorageHelper.storage_service_for_environment  # Returns appropriate service

# In models
has_one_attached :photo, service: StorageHelper.storage_service_for_environment
has_one_attached :file, service: StorageHelper.documents_service
```
