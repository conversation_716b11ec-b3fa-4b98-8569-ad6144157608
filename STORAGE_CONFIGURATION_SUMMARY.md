# Storage Configuration Implementation Summary

## Overview
This implementation adds Rails.env.demo? support and configures environment-specific storage configurations with a unified, dynamic approach that eliminates code duplication.

## Changes Made

### 1. Rails Environment Extensions
- **File**: `config/initializers/rails_env_extensions.rb`
- **Purpose**: Adds `Rails.env.demo?` and `Rails.env.sandbox?` methods
- **Implementation**: Extends Rails::Env class with custom environment detection methods

### 2. Storage Helper
- **File**: `app/helpers/storage_helper.rb`
- **Purpose**: Provides consistent storage service references
- **Key Features**:
  - `storage_service_for_environment`: Returns `:outscale_storage` (now unified)
  - `uploads_service`: Returns uploads service (always `:outscale_uploads`)
  - `documents_service`: Returns documents service (always `:outscale_documents`)

### 3. Unified Storage Configuration
- **File**: `config/storage.yml`
- **Revolutionary Change**: Single `outscale_storage` configuration that dynamically adapts to environment
- **Key Features**:
  - **Dynamic credentials**: Uses `ENV.fetch("OUTSCALE_ACCESS_KEY_ID_#{Rails.env.upcase}", fallback)`
  - **Dynamic bucket selection**: Case statement based on `Rails.env`
  - **Shared sandbox/demo bucket**: Both environments use the same bucket
  - **Zero code duplication**: No repeated configuration blocks

### 4. Model Updates
Updated all models with `has_one_attached` and `has_many_attached` to use the StorageHelper:

#### Models using `StorageHelper.storage_service_for_environment`:
- `Company#logo`
- `Participant#photo`
- `PendingParticipant#photo`
- `KeySet#photo`
- `Site#locker_plan`
- `ConstructionBoard#images`
- `Sanction#image`
- `Stakeholder#logo`
- `SiteDetail#site_image`
- `CompanyPerson#photo`

#### Models using specific services:
- `Document#file` → `StorageHelper.documents_service`
- `Upload#file` → `StorageHelper.uploads_service`

### 5. Environment Configuration
- **All environments**: Now use the single `:outscale_storage` service
- **Dynamic behavior**: The service adapts based on `Rails.env`
- **Unified approach**: No environment-specific service names needed

## Environment Variable Configuration

### Required Environment Variables:
```bash
# Base credentials (used as fallback for all environments)
OUTSCALE_ACCESS_KEY_ID=your_access_key
OUTSCALE_SECRET_ACCESS_KEY=your_secret_key

# Environment-specific credentials (optional, pattern-based)
OUTSCALE_ACCESS_KEY_ID_DEVELOPMENT=dev_access_key
OUTSCALE_SECRET_ACCESS_KEY_DEVELOPMENT=dev_secret_key
OUTSCALE_ACCESS_KEY_ID_SANDBOX=sandbox_access_key
OUTSCALE_SECRET_ACCESS_KEY_SANDBOX=sandbox_secret_key
OUTSCALE_ACCESS_KEY_ID_DEMO=demo_access_key
OUTSCALE_SECRET_ACCESS_KEY_DEMO=demo_secret_key
OUTSCALE_ACCESS_KEY_ID_PRODUCTION=prod_access_key
OUTSCALE_SECRET_ACCESS_KEY_PRODUCTION=prod_secret_key

# Bucket names (optional, with sensible defaults)
OUTSCALE_STORAGE_BUCKET=mashe-storage                    # production
OUTSCALE_STORAGE_DEVELOPMENT_BUCKET=mashe-storage-development
OUTSCALE_STORAGE_SANDBOX_BUCKET=mashe-storage-sandbox    # shared with demo
OUTSCALE_UPLOADS_BUCKET=mashe-fold-uploads
OUTSCALE_DOCUMENTS_BUCKET=mashe-fold-documents
```

## Key Benefits

1. **Ultimate Simplification**: Single storage configuration that adapts to any environment
2. **Zero Code Duplication**: No repeated configuration blocks in storage.yml
3. **Pattern-Based Credentials**: Automatic environment-specific credential lookup
4. **Dynamic Bucket Selection**: Smart bucket assignment based on environment
5. **Shared Resources**: Sandbox and demo environments share the same bucket as requested
6. **Easy Extensibility**: Adding new environments requires only environment variables
7. **Fallback Support**: Graceful fallbacks for missing environment-specific credentials
8. **Clean Code**: Removed repetitive conditional logic from models and configuration

## Testing

The implementation has been tested with a verification script that confirms:
- `Rails.env.demo?` returns `true` only in demo environment
- `Rails.env.sandbox?` returns `true` only in sandbox environment
- Dynamic credential pattern works for all environments
- Sandbox and demo environments share the same bucket
- Single storage service adapts to all environments

## Usage Examples

```ruby
# Check environment
Rails.env.demo?     # true in demo environment
Rails.env.sandbox?  # true in sandbox environment

# Get storage service (now always the same)
StorageHelper.storage_service_for_environment  # Always returns :outscale_storage

# In models (unchanged usage)
has_one_attached :photo, service: StorageHelper.storage_service_for_environment
has_one_attached :file, service: StorageHelper.documents_service
```

## Storage Configuration Details

The unified `outscale_storage` configuration in `storage.yml`:

```yaml
outscale_storage:
  service: S3
  access_key_id: <%= ENV.fetch("OUTSCALE_ACCESS_KEY_ID_#{Rails.env.upcase}", ENV['OUTSCALE_ACCESS_KEY_ID']) %>
  secret_access_key: <%= ENV.fetch("OUTSCALE_SECRET_ACCESS_KEY_#{Rails.env.upcase}", ENV['OUTSCALE_SECRET_ACCESS_KEY']) %>
  region: cloudgouv-eu-west-1
  bucket: <%= case Rails.env; when 'development'; ENV.fetch('OUTSCALE_STORAGE_DEVELOPMENT_BUCKET', 'mashe-storage-development'); when 'sandbox', 'demo'; ENV.fetch('OUTSCALE_STORAGE_SANDBOX_BUCKET', 'mashe-storage-sandbox'); else; ENV.fetch('OUTSCALE_STORAGE_BUCKET', 'mashe-storage'); end %>
  endpoint: https://oos.cloudgouv-eu-west-1.outscale.com
  force_path_style: true
```

This single configuration automatically:
- Looks for environment-specific credentials first, falls back to base credentials
- Selects the appropriate bucket based on the current Rails environment
- Ensures sandbox and demo share the same bucket
- Works for any environment without code changes
