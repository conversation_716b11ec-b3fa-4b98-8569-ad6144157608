# Configuration de Stockage - Résumé Final

## Vue d'ensemble
Implémentation de `Rails.env.demo?` et configuration unifiée du stockage avec buckets spécifiques par environnement pour tous les services (storage, uploads, documents).

## Changements Effectués

### 1. Extensions d'Environnement Rails
- **Fichier**: `config/initializers/rails_env_extensions.rb`
- **Objectif**: Ajoute les méthodes `Rails.env.demo?` et `Rails.env.sandbox?`
- **Implémentation**: Extension de la classe Rails::Env

### 2. Service de Stockage
- **Fichier**: `app/services/storage_service.rb`
- **Objectif**: Fournit des références cohérentes aux services de stockage
- **Architecture**: Service approprié (pas un helper qui est pour les vues)
- **Méthodes**:
  - `storage_service`: Retourne `:outscale_storage`
  - `uploads_service`: Retourne `:outscale_uploads`
  - `documents_service`: Retourne `:outscale_documents`

### 3. Configuration Unifiée du Stockage
- **Fichier**: `config/storage.yml`
- **Innovation**: Configurations dynamiques avec buckets spécifiques par environnement
- **Caractéristiques**:
  - **Credentials uniques**: Mêmes credentials pour tous les environnements
  - **Buckets dynamiques**: Sélection automatique basée sur `Rails.env`
  - **Sandbox/Demo partagés**: Utilisent les mêmes buckets

### 4. Mise à Jour des Modèles
Tous les modèles utilisent maintenant `StorageService` :

#### Modèles utilisant le stockage principal :
- `Company#logo` → `StorageService.storage_service`
- `Participant#photo` → `StorageService.storage_service`
- `PendingParticipant#photo` → `StorageService.storage_service`
- `KeySet#photo` → `StorageService.storage_service`
- `Site#locker_plan` → `StorageService.storage_service`
- `ConstructionBoard#images` → `StorageService.storage_service`
- `Sanction#image` → `StorageService.storage_service`
- `Stakeholder#logo` → `StorageService.storage_service`
- `SiteDetail#site_image` → `StorageService.storage_service`
- `CompanyPerson#photo` → `StorageService.storage_service`

#### Modèles utilisant des services spécialisés :
- `Document#file` → `StorageService.documents_service`
- `Upload#file` → `StorageService.uploads_service`

## Configuration des Variables d'Environnement

### Variables Requises :
```bash
# Credentials (identiques pour tous les environnements)
OUTSCALE_ACCESS_KEY_ID=your_access_key
OUTSCALE_SECRET_ACCESS_KEY=your_secret_key

# Buckets de stockage principal (photos, images, etc.)
OUTSCALE_STORAGE_BUCKET=mashe-storage                    # production
OUTSCALE_STORAGE_DEVELOPMENT_BUCKET=mashe-storage-development
OUTSCALE_STORAGE_SANDBOX_BUCKET=mashe-storage-sandbox    # partagé avec demo

# Buckets pour les uploads
OUTSCALE_UPLOADS_BUCKET=mashe-fold-uploads               # production
OUTSCALE_UPLOADS_DEVELOPMENT_BUCKET=mashe-fold-uploads-development
OUTSCALE_UPLOADS_SANDBOX_BUCKET=mashe-fold-uploads-sandbox # partagé avec demo

# Buckets pour les documents traités
OUTSCALE_DOCUMENTS_BUCKET=mashe-fold-documents           # production
OUTSCALE_DOCUMENTS_DEVELOPMENT_BUCKET=mashe-fold-documents-development
OUTSCALE_DOCUMENTS_SANDBOX_BUCKET=mashe-fold-documents-sandbox # partagé avec demo
```

## Avantages Clés

1. **Architecture Correcte**: Service au lieu d'un helper mal placé
2. **Credentials Unifiés**: Mêmes credentials pour tous les environnements
3. **Buckets Spécialisés**: Séparation claire par type et environnement
4. **Sandbox/Demo Partagés**: Buckets communs comme demandé
5. **Configuration Dynamique**: Sélection automatique basée sur l'environnement
6. **Zéro Duplication**: Configuration unifiée dans storage.yml
7. **Extensibilité**: Facile d'ajouter de nouveaux environnements

## Règles de Nommage des Buckets
- **Production**: `mashe-{type}` (ex: `mashe-storage`)
- **Development**: `mashe-{type}-development`
- **Sandbox/Demo**: `mashe-{type}-sandbox` (partagé)

## Utilisation

```ruby
# Vérification d'environnement
Rails.env.demo?     # true en environnement demo
Rails.env.sandbox?  # true en environnement sandbox

# Services de stockage
StorageService.storage_service    # :outscale_storage
StorageService.uploads_service    # :outscale_uploads
StorageService.documents_service  # :outscale_documents

# Dans les modèles
has_one_attached :photo, service: StorageService.storage_service
has_one_attached :file, service: StorageService.documents_service
```
