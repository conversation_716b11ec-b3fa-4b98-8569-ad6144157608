# Structuration du code: Guide de référence

## Répartition des responsabilités

### 📋 Dans les MODÈLES

Questions à se poser:

- Est-ce que cela concerne les données fondamentales de l'objet?
- Est-ce une validation ou contrainte d'intégrité?
- Est-ce une relation avec d'autres modèles?
- Est-ce un calcul simple basé uniquement sur les attributs de l'objet?

Exemples:
- Validations (`validates :name, presence: true`)
- Relations (`belongs_to`, `has_many`)
- Callbacks simples liés au cycle de vie
- Méthodes d'instance simples manipulant uniquement les attributs internes:

```ruby
def full_name
  "#{first_name} #{last_name}"
end
```

### 🗄️ Dans les REPOSITORIES
Questions à se poser:
- Est-ce une requête complexe avec plusieurs jointures?
- Est-ce utilisé dans plusieurs endroits de l'application?
- Suis-je en train de filtrer, trier ou agréger des données?
- Cette requête risque-t-elle de générer des problèmes N+1?

Exemples:
```ruby
#app/repositories/participant_repository.rb
class ParticipantRepository
  class << self
    def active_with_valid_documents(site_id)
      Participant.of_site(site_id)
        .includes(:documents)
        .where(documents: { status: :active, valid_until: Date.today.. })
        .distinct
    end
  end
end
```

### 🖼️ Dans les PRESENTERS
Questions à se poser:
- Cette méthode sert-elle uniquement à l'affichage?
- Est-ce que je transforme des données pour une vue spécifique?
- Est-ce que je combine plusieurs objets pour un affichage?
- Est-ce que j'optimise un affichage de liste (comme DataTables)?

Exemples:
```ruby
#app/presenters/data_tables/locker_presenter.rb
module DataTables
  class LockerPresenter
    def initialize(lockers)
      @lockers = lockers
    end

    def with_preloaded_data
      @lockers.includes(:active_locker_participants)
        .preload(:locker_damages)
    end

    def as_json
      with_preloaded_data.map do |locker|
        {
        zone_number: "#{locker.zone}-#{locker.number}",
        status: locker.active_locker_participants.any? ? "Occupé" : "Libre"
        }
      end
    end
  end
end
```

### 🔍 Dans les QUERIES
Questions à se poser:
- Est-ce une requête très spécifique à un cas d'utilisation unique?
- La requête est-elle particulièrement complexe avec des logiques conditionnelles?
- Est-ce que je dois paramétrer finement la requête?

Exemples:
```ruby
#app/queries/participants_with_expired_documents_query.rb
class ParticipantsWithExpiredDocumentsQuery
  def initialize(site_id, include_pending: false)
  @site_id = site_id
  @include_pending = include_pending
  end

  def call
    base_query = Participant.of_site(@site_id)
                            .joins(:documents)

    if @include_pending
      base_query = base_query.where("documents.status IN ('active', 'pending')")
    else
      base_query = base_query.where(documents: { status: :active })
    end

    base_query.where("documents.valid_until < ?", Date.today)
              .distinct
  end
end
```

## Règles générales de décision
Un modèle devrait:
- Connaître ses attributs et relations
- Valider son propre état
- Fournir des méthodes d'accès simples à ses propriétés

Un repository devrait:
- Centraliser les requêtes complexes pour un modèle
- Optimiser les chargements avec includes, preload, etc.
- Fournir une API claire pour accéder aux données

Un presenter devrait:
- Transformer les données pour un contexte d'affichage spécifique
- Précharger efficacement les données pour des listes
- Séparer la logique de présentation du modèle

Un query object devrait:
- Encapsuler une requête complexe unique
- Accepter des paramètres pour ajuster la requête
- Retourner un objet ActiveRecord pour chaînage

## Variables d'environnement pour le stockage

### Configuration des buckets par environnement

```bash
# Buckets de stockage principal (photos, images, etc.)
OUTSCALE_STORAGE_BUCKET=mashe-storage                    # production
OUTSCALE_STORAGE_DEVELOPMENT_BUCKET=mashe-storage-development
OUTSCALE_STORAGE_SANDBOX_BUCKET=mashe-storage-sandbox    # partagé avec demo

# Buckets pour les uploads
OUTSCALE_UPLOADS_BUCKET=mashe-fold-uploads               # production
OUTSCALE_UPLOADS_DEVELOPMENT_BUCKET=mashe-fold-uploads-development
OUTSCALE_UPLOADS_SANDBOX_BUCKET=mashe-fold-uploads-sandbox # partagé avec demo

# Buckets pour les documents traités
OUTSCALE_DOCUMENTS_BUCKET=mashe-fold-documents           # production
OUTSCALE_DOCUMENTS_DEVELOPMENT_BUCKET=mashe-fold-documents-development
OUTSCALE_DOCUMENTS_SANDBOX_BUCKET=mashe-fold-documents-sandbox # partagé avec demo

# Credentials (identiques pour tous les environnements)
OUTSCALE_ACCESS_KEY_ID=your_access_key
OUTSCALE_SECRET_ACCESS_KEY=your_secret_key
```

### Règles de nommage des buckets
- **Production**: `mashe-{type}` (ex: `mashe-storage`)
- **Development**: `mashe-{type}-development` (ex: `mashe-storage-development`)
- **Sandbox/Demo**: `mashe-{type}-sandbox` (ex: `mashe-storage-sandbox`) - partagé entre sandbox et demo

## Indicateurs d'alerte
  🚩 Méthode de modèle trop longue (> 5 lignes) → Considérer un service ou repository
  🚩 Méthode avec plusieurs jointures → Déplacer vers un repository
  🚩 Logique de formattage dans un modèle → Déplacer vers un presenter
  🚩 Requête utilisée dans un seul endroit → Considérer un query object plutôt qu'un repository
  🚩 Modèle avec trop de méthodes (>20) → Probablement besoin de décomposition
